# 🚀 Diagrama de Implementación Detallado - Sistema de Prompts Markdown

## 📊 Estado Actual del Proyecto

### ✅ **Componentes Implementados**
```
src/Core/
├── ✅ markdown_prompt_loader.py    # Loader completo con cache
├── ✅ prompt_markdown_parser.py    # Parser con validación
├── ✅ prompt_service.py           # Servicio integrado con LLM
└── ⚠️ prompt_validator.py         # Pendiente implementar

prompts/
└── user-story/
    ├── ✅ enhance.md              # Primer prompt migrado
    └── ✅ metadata.json           # Metadatos configurados
```

### 🔄 **Componentes Legacy (A Migrar)**
```
src/Prompts/
├── 🔴 agno_prompts.py             # Funciones con prompts embebidos
├── 🔴 agno_prompts_clean.py       # Prompts legacy limpios  
├── 🔴 browser_prompts.py          # Prompts dinámicos browser
├── 🔴 code_gen_prompts.py         # Generación de código
├── 🔴 gherkin_prompts.py          # Conversión a Gherkin
├── 🔴 test_case_prompts.py        # Casos de prueba manuales
├── 🔴 test_summarization_prompts.py # Resumen de resultados
├── 🔴 user_story_prompts.py       # Ya migrado ✅
└── ⚠️ prompt_manager.py           # Manager básico actual
```

---

## 🎯 Plan de Implementación Fase por Fase

### **FASE 1: Completar Infraestructura Base** (Días 1-2)

#### **Día 1: Validador de Prompts** 🔧
```mermaid
flowchart TD
    A[Crear PromptValidator] --> B[Validar estructura MD]
    B --> C[Validar variables]
    C --> D[Validar traducciones]
    D --> E[Tests unitarios]
    
    style A fill:#e1f5fe
    style E fill:#c8e6c9
```

**Archivos a crear:**
- `src/Core/prompt_validator.py`
- `tests/test_prompt_validator.py`

**Funcionalidades:**
- Validar secciones obligatorias en Markdown
- Verificar consistencia de variables entre idiomas
- Validar archivos metadata.json
- Reportar errores detallados

#### **Día 2: CLI de Gestión** 🖥️
```mermaid
flowchart LR
    A[prompt_cli.py] --> B[Listar prompts]
    A --> C[Validar todos]
    A --> D[Crear nuevo]
    A --> E[Testear prompt]
    
    style A fill:#fff3e0
    style B fill:#f3e5f5
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#f3e5f5
```

**Comandos a implementar:**
```bash
python prompt_cli.py list                                    # Lista todos los prompts
python prompt_cli.py validate                               # Valida estructura
python prompt_cli.py create --category test-cases --id api   # Crea nuevo prompt
python prompt_cli.py test --category user-story --id enhance # Testea prompt
```

---

### **FASE 2: Migración Masiva de Prompts** (Días 3-7)

#### **Prioridad de Migración:**

```mermaid
gantt
    title Migración de Prompts por Prioridad
    dateFormat  YYYY-MM-DD
    section Alta Prioridad
    test_case_prompts.py     :active, p1, 2025-05-31, 1d
    gherkin_prompts.py       :p2, after p1, 1d
    code_gen_prompts.py      :p3, after p2, 2d
    
    section Media Prioridad
    test_summarization       :p4, after p3, 1d
    browser_prompts.py       :p5, after p4, 1d
    
    section Baja Prioridad
    agno_prompts_clean.py    :p6, after p5, 1d
    agno_prompts.py          :p7, after p6, 1d
```

#### **Día 3: Test Cases + Gherkin** 📝
**Migrar:**
- `test_case_prompts.py` → `prompts/test-cases/manual-generation.md`
- `gherkin_prompts.py` → `prompts/test-cases/gherkin-conversion.md`

**Estructura:**
```
prompts/test-cases/
├── manual-generation.md      # Generación de casos de prueba
├── gherkin-conversion.md     # Conversión a Gherkin BDD
└── metadata.json            # Metadatos de la categoría
```

#### **Días 4-5: Code Generation** 💻
**Migrar:**
- `code_gen_prompts.py` → `prompts/code-generation/*.md`

**Estructura:**
```
prompts/code-generation/
├── selenium-pytest.md       # Selenium + PyTest
├── playwright-python.md     # Playwright Python
├── cypress-javascript.md    # Cypress JS
├── robot-framework.md       # Robot Framework
├── java-selenium.md         # Java + Selenium
└── metadata.json           # Metadatos
```

#### **Día 6: Test Analysis** 📊
**Migrar:**
- `test_summarization_prompts.py` → `prompts/test-analysis/results-summary.md`

#### **Día 7: Browser Automation** 🌐
**Migrar:**
- `browser_prompts.py` → `prompts/browser-automation/task-generation.md`

---

### **FASE 3: Integración y Limpieza** (Días 8-10)

#### **Día 8: Actualizar app.py** 🔗
```mermaid
flowchart TD
    A[app.py actual] --> B[Identificar llamadas legacy]
    B --> C[Reemplazar con PromptService]
    C --> D[Testing funcional]
    D --> E[Verificar UI]
    
    style A fill:#ffebee
    style E fill:#e8f5e8
```

**Cambios necesarios:**
- Reemplazar imports de `src/Prompts/*` 
- Usar `PromptService` en lugar de funciones directas
- Actualizar parámetros de llamadas
- Mantener compatibilidad con UI

#### **Día 9: Migración Legacy** 🧹
**Migrar archivos legacy:**
- `agno_prompts_clean.py` → Extraer prompts embebidos
- `agno_prompts.py` → Extraer y limpiar prompts

#### **Día 10: Testing E2E** ✅
- Tests de integración completos
- Verificación de performance
- Validación de todas las funcionalidades
- Rollback plan

---

## 🏗️ Arquitectura Detallada del Sistema

### **Flujo de Ejecución Completo:**

```mermaid
sequenceDiagram
    participant UI as 🖥️ Streamlit UI
    participant PS as 🎯 PromptService
    participant ML as 📂 MarkdownLoader
    participant MP as 📝 MarkdownParser
    participant LLM as 🤖 Google Gemini
    
    UI->>PS: enhance_user_story("Login feature")
    PS->>ML: load_prompt("user-story", "enhance", "en")
    ML->>MP: parse_prompt_file("enhance.md")
    MP-->>ML: parsed_sections
    ML-->>PS: prompt_template
    PS->>PS: substitute_variables(user_story="Login feature")
    PS->>LLM: invoke(final_prompt)
    LLM-->>PS: enhanced_story
    PS-->>UI: result
```

### **Estructura de Directorios Final:**

```
AgentQA/
├── prompts/                          # 🟢 NUEVO SISTEMA
│   ├── user-story/
│   │   ├── enhance.md                # ✅ Implementado
│   │   └── metadata.json             # ✅ Implementado
│   ├── test-cases/
│   │   ├── manual-generation.md      # 📋 Día 3
│   │   ├── gherkin-conversion.md     # 📋 Día 3
│   │   └── metadata.json
│   ├── code-generation/
│   │   ├── selenium-pytest.md       # 📋 Día 4
│   │   ├── playwright-python.md     # 📋 Día 4
│   │   ├── cypress-javascript.md    # 📋 Día 4
│   │   ├── robot-framework.md       # 📋 Día 5
│   │   ├── java-selenium.md         # 📋 Día 5
│   │   └── metadata.json
│   ├── test-analysis/
│   │   ├── results-summary.md       # 📋 Día 6
│   │   └── metadata.json
│   ├── browser-automation/
│   │   ├── task-generation.md       # 📋 Día 7
│   │   └── metadata.json
│   └── shared/
│       ├── templates/
│       └── variables.json
│
├── src/Core/                         # 🟢 SISTEMA CORE
│   ├── markdown_prompt_loader.py     # ✅ Implementado
│   ├── prompt_markdown_parser.py     # ✅ Implementado
│   ├── prompt_service.py             # ✅ Implementado
│   └── prompt_validator.py           # 📋 Día 1
│
├── src/Prompts/                      # 🔴 LEGACY (eliminar)
│   └── [archivos a migrar]
│
├── prompt_cli.py                     # 📋 Día 2
└── test_prompt_system.py             # ✅ Implementado
```

---

## 🛠️ Templates y Generadores

### **Template para Nuevos Prompts:**

```markdown
# [Nombre del Prompt]

## Purpose
[Descripción clara y concisa del propósito]

## Input Format
- variable_1: Descripción de la primera variable
- variable_2: Descripción de la segunda variable

## Output Format
- Formato esperado de la respuesta
- Estructura específica si aplica

## English Prompt

[Prompt en inglés con variables {variable_1} y {variable_2}]

## Spanish Prompt

[Prompt en español con variables {variable_1} y {variable_2}]

## Variables
- `{variable_1}` - Descripción detallada de la variable
- `{variable_2}` - Descripción detallada de la variable

## Examples

### Input
```
variable_1: ejemplo
variable_2: ejemplo
```

### Output
```
[Salida esperada del ejemplo]
```

## Validation Rules
- Regla 1: Descripción
- Regla 2: Descripción

## Version History
- v1.0.0 (2025-05-31): Creación inicial
```

### **Template metadata.json:**

```json
{
  "category": "nombre-categoria",
  "version": "1.0.0",
  "description": "Descripción de la categoría",
  "prompts": [
    {
      "id": "prompt-id",
      "name": "Nombre Descriptivo",
      "description": "Descripción del prompt",
      "file": "archivo.md",
      "languages": ["en", "es"],
      "variables": ["variable_1", "variable_2"],
      "tags": ["tag1", "tag2"],
      "lastModified": "2025-05-31T12:00:00Z",
      "author": "AgentQA Team"
    }
  ]
}
```

---

## 🚀 Comandos de Inicio Rápido

### **Día 1: Crear el Validador**
```bash
# 1. Crear el validador
touch src/Core/prompt_validator.py

# 2. Crear tests
mkdir -p tests/core
touch tests/core/test_prompt_validator.py

# 3. Testear funcionalidad básica
python test_prompt_system.py
```

### **Día 2: CLI de Gestión**
```bash
# 1. Crear CLI
touch prompt_cli.py

# 2. Testear comandos básicos
python prompt_cli.py list
python prompt_cli.py validate
```

### **Día 3: Migración Test Cases**
```bash
# 1. Crear directorio
mkdir -p prompts/test-cases

# 2. Crear archivos base
touch prompts/test-cases/manual-generation.md
touch prompts/test-cases/gherkin-conversion.md
touch prompts/test-cases/metadata.json

# 3. Testear carga
python prompt_cli.py validate
```

---

## 📊 Métricas de Éxito

### **Técnicas:**
- [ ] ✅ 100% de prompts migrados (0 en archivos .py)
- [ ] ✅ Tiempo de carga < 200ms con cache
- [ ] ✅ Cobertura de tests > 90%
- [ ] ✅ 0 errores en validación automática

### **Funcionales:**
- [ ] ✅ Usuario no técnico puede editar prompts
- [ ] ✅ Hot reload sin reiniciar app
- [ ] ✅ Performance igual o mejor que sistema actual
- [ ] ✅ Rollback funcional en < 5 minutos

### **Arquitectónicas:**
- [ ] ✅ Separación completa prompts/código
- [ ] ✅ Sistema escalable para nuevos prompts
- [ ] ✅ Versionado granular implementado
- [ ] ✅ Cache eficiente implementado

---

## 🎯 Próximos Pasos Inmediatos

### **AHORA MISMO:**
1. **Completar PromptValidator** (`src/Core/prompt_validator.py`)
2. **Crear CLI básico** (`prompt_cli.py`)
3. **Testear sistema actual** con prompts existentes

### **ESTA SEMANA:**
1. **Migrar test_case_prompts.py** → Markdown
2. **Migrar gherkin_prompts.py** → Markdown  
3. **Migrar code_gen_prompts.py** → Markdown
4. **Crear templates reutilizables**

### **PRÓXIMA SEMANA:**
1. **Integrar con app.py** principal
2. **Eliminar archivos legacy**
3. **Testing E2E completo**
4. **Documentación final**

---

## 🔄 Diagrama de Migración Específico

```mermaid
graph LR
    subgraph "🔴 ACTUAL"
        A1[test_case_prompts.py]
        A2[gherkin_prompts.py]
        A3[code_gen_prompts.py]
        A4[browser_prompts.py]
        A5[test_summarization_prompts.py]
    end
    
    subgraph "🔄 PROCESO"
        B1[Extraer prompts]
        B2[Crear MD files]
        B3[Crear metadata]
        B4[Validar estructura]
        B5[Testear carga]
    end
    
    subgraph "✅ NUEVO"
        C1[prompts/test-cases/manual-generation.md]
        C2[prompts/test-cases/gherkin-conversion.md]
        C3[prompts/code-generation/*.md]
        C4[prompts/browser-automation/task-generation.md]
        C5[prompts/test-analysis/results-summary.md]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> B5
    
    B5 --> C1
    B5 --> C2
    B5 --> C3
    B5 --> C4
    B5 --> C5
```

---

**📅 Fecha de Creación:** 30 Mayo 2025  
**👨‍💻 Arquitecto:** GitHub Copilot  
**🎯 Estado:** Ready for Implementation  
**⏰ Duración Estimada:** 10 días laborables
