#!/usr/bin/env python3
"""
Test script to verify the smoke test configuration is working correctly.
"""

import os
import sys
import asyncio

# Add the project root to the Python path
sys.path.append('.')

def test_config_creation():
    """Test that the configuration can be created without errors."""
    try:
        from src.Utilities.browser_helper import create_fast_config, validate_config
        
        # Create fast config for smoke tests
        config = create_fast_config()
        
        print("✅ Fast config created successfully:")
        print(f"  - Headless: {config.headless}")
        print(f"  - Vision: {config.use_vision}")
        print(f"  - Memory: {config.enable_memory}")
        print(f"  - Max steps: {config.max_steps}")
        print(f"  - Viewport expansion: {config.viewport_expansion}")
        print(f"  - Highlight elements: {config.highlight_elements}")
        
        # Validate config
        warnings = validate_config(config)
        if warnings:
            print(f"⚠️ Configuration warnings: {warnings}")
        else:
            print("✅ No configuration warnings")
            
        return True
        
    except Exception as e:
        print(f"❌ Error creating config: {e}")
        return False

def test_scenario_generation():
    """Test that Gherkin scenarios can be generated."""
    try:
        from src.Utilities.test_executor import TestExecutor
        
        # Create test executor
        executor = TestExecutor(api_key="test_key", language="en")
        
        # Generate a simple scenario
        scenario = executor.create_gherkin_scenario(
            instructions="Verify the page loads correctly",
            url="https://example.com",
            user_story="As a user, I want to verify basic functionality"
        )
        
        print("✅ Gherkin scenario generated successfully:")
        print(scenario)
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating scenario: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Testing Smoke Test Configuration")
    print("=" * 50)
    
    # Test configuration creation
    config_ok = test_config_creation()
    print()
    
    # Test scenario generation
    scenario_ok = test_scenario_generation()
    print()
    
    # Summary
    print("📋 Test Summary:")
    print(f"  - Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"  - Scenario Generation: {'✅ PASS' if scenario_ok else '❌ FAIL'}")
    
    if config_ok and scenario_ok:
        print("\n🎉 All tests passed! Smoke test configuration is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the configuration.")
        return 1

if __name__ == "__main__":
    exit(main())
