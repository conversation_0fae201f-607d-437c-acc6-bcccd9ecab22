#!/usr/bin/env python3
import json
from pathlib import Path

def test_basic_functionality():
    """Test basic file access and JSON parsing"""
    print("Testing basic functionality...")
    
    # Test file access
    prompts_dir = Path("prompts")
    category_dir = prompts_dir / "test-cases"
    metadata_file = category_dir / "metadata.json"
    
    print(f"Prompts dir exists: {prompts_dir.exists()}")
    print(f"Category dir exists: {category_dir.exists()}")
    print(f"Metadata file exists: {metadata_file.exists()}")
    
    if metadata_file.exists():
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            print("✅ Metadata loaded successfully")
            
            # Find manual-generation prompt
            for prompt in metadata.get("prompts", []):
                if prompt.get("id") == "manual-generation":
                    print(f"✅ Found manual-generation prompt")
                    file_path = category_dir / prompt["file"]
                    print(f"Prompt file exists: {file_path.exists()}")
                    
                    if file_path.exists():
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        print(f"✅ File read successfully: {len(content)} characters")
                        return True
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return False

if __name__ == "__main__":
    test_basic_functionality()
