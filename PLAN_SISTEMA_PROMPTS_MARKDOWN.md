# 📝 Plan de Sistema de Prompts Basado en Markdown - AgentQA

## 🎯 Objetivo

Crear un **sistema de gestión de prompts basado en archivos Markdown** que permita modificar prompts sin tocar código, facilitando el mantenimiento, versionado y colaboración por parte de personas no técnicas.

---

## 📊 Inventario Actual y Propósito de Cada Prompt

### **Prompts de Mejora y Transformación**

#### `user_story_enhance` 
- **Propósito**: Mejora historias de usuario para seguir formato estándar
- **Entrada**: Historia de usuario básica o mal estructurada  
- **Salida**: Historia mejorada con formato "Como [rol], quiero [funcionalidad] para [beneficio]"
- **Ejemplo**: `"Login feature" → "Como usuario registrado, quiero iniciar sesión con mi email y contraseña para acceder a mi cuenta personal"`

#### `test_case_manual`
- **Propósito**: Genera casos de prueba manuales detallados
- **Entrada**: Historia de usuario o funcionalidad
- **Salida**: Lista de casos de prueba con pasos, datos de entrada y resultados esperados
- **Ejemplo**: Para login → casos de prueba válidos, inválidos, campos vacíos, etc.

#### `gherkin_generate`
- **Propósito**: Convierte casos de prueba a formato Gherkin (BDD)
- **Entrada**: Casos de prueba manuales
- **Salida**: Escenarios en formato Given/When/Then
- **Ejemplo**: `Given usuario en página login, When ingresa credenciales válidas, Then accede al dashboard`

#### `test_summarize_results`
- **Propósito**: Resume resultados de ejecuciones de pruebas
- **Entrada**: Logs, reportes o resultados de testing
- **Salida**: Resumen ejecutivo con estadísticas y hallazgos principales

### **Prompts de Generación de Código**

#### `code_gen_selenium_pytest`
- **Propósito**: Genera código automatizado Selenium + PyTest
- **Entrada**: Escenarios Gherkin o casos de prueba manuales
- **Salida**: Código Python con Selenium WebDriver y PyTest

#### `code_gen_playwright`
- **Propósito**: Genera código automatizado con Playwright
- **Entrada**: Escenarios de prueba
- **Salida**: Código Python con Playwright para automatización web

#### `code_gen_cypress`
- **Propósito**: Genera código automatizado con Cypress
- **Entrada**: Escenarios de prueba
- **Salida**: Código JavaScript con Cypress para testing E2E

#### `code_gen_robot_framework`
- **Propósito**: Genera código con Robot Framework
- **Entrada**: Casos de prueba
- **Salida**: Archivos .robot con keywords y test cases

#### `code_gen_java_selenium`
- **Propósito**: Genera código Java con Selenium
- **Entrada**: Escenarios de prueba
- **Salida**: Código Java con TestNG/JUnit y Selenium WebDriver

### **Prompts Dinámicos Especializados**

#### `generate_browser_task` (browser_prompts.py)
- **Propósito**: Crea tareas de automatización de navegador
- **Entrada**: Contexto de navegación, URLs, acciones previas
- **Salida**: Instrucciones específicas para automatización con preservación de contexto

### **Funciones Legacy en Migración** 

#### Desde `agno_prompts_new.py` (⚠️ Requiere migración):
- `enhance_user_story()` - **Duplicado** con user_story_enhance
- `generate_manual_test_cases()` - **Duplicado** con test_case_manual  
- `generate_gherkin_scenarios()` - **Similar** a gherkin_generate pero con lógica adicional
- `generate_selenium_pytest_bdd()` - **Similar** a code_gen_selenium_pytest
- `generate_playwright_python()` - **Similar** a code_gen_playwright
- `generate_cypress_js()` - **Similar** a code_gen_cypress
- `generate_robot_framework()` - **Similar** a code_gen_robot_framework
- `generate_java_selenium()` - **Similar** a code_gen_java_selenium

---

## 🏗️ Arquitectura del Sistema Propuesto

### **Estructura de Directorios**
```
prompts/
├── user-story/
│   ├── enhance.md
│   └── metadata.json
├── test-cases/
│   ├── manual-generation.md
│   ├── gherkin-conversion.md
│   └── metadata.json
├── code-generation/
│   ├── selenium-pytest.md
│   ├── playwright-python.md
│   ├── cypress-javascript.md
│   ├── robot-framework.md
│   ├── java-selenium.md
│   └── metadata.json
├── test-analysis/
│   ├── results-summary.md
│   └── metadata.json
├── browser-automation/
│   ├── task-generation.md
│   └── metadata.json
└── shared/
    ├── templates/
    └── variables.json
```

### **Formato de Archivos Markdown**

#### **Archivo de Prompt** (`enhance.md`)
```markdown
# User Story Enhancement

## Purpose
Improve user stories to follow standard format and include complete acceptance criteria.

## Input Format
- Raw user story text
- Basic feature description

## Output Format  
- Standard format: "As a [role], I want [functionality] to [benefit]"
- Clear acceptance criteria
- Specific examples when applicable

## English Prompt

Your task is to improve the following user story to be clearer, more complete, and follow the standard format 'As a [role], I want [functionality] to [benefit]'.

Make sure to include:
1. The specific user role (Who)
2. The desired functionality (What)  
3. The expected benefit or value (Why)
4. Clear and specific acceptance criteria
5. Concrete examples if possible

Original story:
{user_story}

IMPORTANT: Provide ONLY the improved user story content. Do not include any introductory text, explanations, or markdown formatting. Return only the clean, enhanced user story text.

## Spanish Prompt

Tu tarea es mejorar la siguiente historia de usuario para que sea más clara, completa y siga el formato estándar 'Como [rol], quiero [funcionalidad] para [beneficio]'.

Asegúrate de incluir:
1. El rol específico del usuario (Quién)
2. La funcionalidad deseada (Qué)
3. El beneficio o valor esperado (Por qué)
4. Criterios de aceptación claros y específicos
5. Ejemplos concretos si es posible

Historia original:
{user_story}

IMPORTANTE: Proporciona ÚNICAMENTE el contenido de la historia de usuario mejorada. No incluyas texto introductorio, explicaciones o formato markdown. Devuelve solo el texto limpio de la historia de usuario mejorada.

## Variables
- `{user_story}` - The original user story to be enhanced

## Examples

### Input
```
Login feature
```

### Output
```
As a registered user, I want to log into the system using my email and password so that I can access my personal account and manage my data securely.

Acceptance Criteria:
- User can enter email and password in the login form
- System validates credentials against the database
- Successful login redirects to the user dashboard
- Failed login shows appropriate error message
- Password field is masked for security
- Login session expires after 30 minutes of inactivity
```
```

#### **Archivo de Metadatos** (`metadata.json`)
```json
{
  "category": "user-story",
  "version": "1.0.0",
  "prompts": [
    {
      "id": "enhance",
      "name": "User Story Enhancement", 
      "description": "Improves user stories to follow standard format",
      "file": "enhance.md",
      "languages": ["en", "es"],
      "variables": ["user_story"],
      "tags": ["enhancement", "user-story", "format"],
      "lastModified": "2025-05-30T12:00:00Z",
      "author": "AgentQA Team"
    }
  ]
}
```

---

## 🔧 Componentes del Sistema

### **1. Loader de Prompts** (`prompt_loader.py`)
```python
class MarkdownPromptLoader:
    def __init__(self, prompts_dir: str = "prompts"):
        self.prompts_dir = prompts_dir
        self._cache = {}
    
    def load_prompt(self, category: str, prompt_id: str, language: str = "en") -> str:
        """Carga un prompt desde archivo Markdown"""
        
    def get_available_prompts(self) -> Dict[str, List[str]]:
        """Lista todos los prompts disponibles por categoría"""
        
    def validate_prompt(self, category: str, prompt_id: str) -> bool:
        """Valida que un prompt esté bien formateado"""
```

### **2. Parser de Markdown** (`markdown_parser.py`)
```python
class PromptMarkdownParser:
    def parse_prompt_file(self, file_path: str) -> Dict[str, Any]:
        """Extrae prompts en múltiples idiomas desde Markdown"""
        
    def extract_variables(self, prompt_text: str) -> List[str]:
        """Extrae variables {variable_name} del prompt"""
        
    def substitute_variables(self, prompt_text: str, **kwargs) -> str:
        """Sustituye variables en el prompt"""
```

### **3. Validador de Prompts** (`prompt_validator.py`)
```python
class PromptValidator:
    def validate_structure(self, prompt_file: str) -> List[str]:
        """Valida estructura del archivo Markdown"""
        
    def validate_variables(self, prompt_text: str, required_vars: List[str]) -> bool:
        """Valida que todas las variables requeridas estén presentes"""
        
    def validate_translations(self, prompt_data: Dict) -> List[str]:
        """Valida que existan traducciones para todos los idiomas"""
```

### **4. Servicio de Prompts** (`prompt_service.py`)
```python
class PromptService:
    def __init__(self, prompts_dir: str = "prompts"):
        self.loader = MarkdownPromptLoader(prompts_dir)
        self.llm = ChatGoogleGenerativeAI(...)
    
    def execute_prompt(self, category: str, prompt_id: str, language: str = "en", **variables) -> str:
        """Ejecuta un prompt con las variables proporcionadas"""
        
    def enhance_user_story(self, user_story: str, language: str = "en") -> str:
        return self.execute_prompt("user-story", "enhance", language, user_story=user_story)
        
    def generate_test_cases(self, user_story: str, language: str = "en") -> str:
        return self.execute_prompt("test-cases", "manual-generation", language, user_story=user_story)
```

---

## 📂 Migración por Fases

### **Fase 1: Infraestructura Base** (2-3 días)
1. ✅ Crear estructura de directorios `prompts/`
2. ✅ Implementar `MarkdownPromptLoader`
3. ✅ Implementar `PromptMarkdownParser`
4. ✅ Crear `PromptValidator`
5. ✅ Tests unitarios básicos

### **Fase 2: Migración de Prompts Estructurados** (3-4 días)
1. ✅ Migrar `user_story_prompts.py` → `prompts/user-story/enhance.md`
2. ✅ Migrar `test_case_prompts.py` → `prompts/test-cases/manual-generation.md`
3. ✅ Migrar `gherkin_prompts.py` → `prompts/test-cases/gherkin-conversion.md`
4. ✅ Migrar `code_gen_prompts.py` → `prompts/code-generation/*.md`
5. ✅ Migrar `test_summarization_prompts.py` → `prompts/test-analysis/results-summary.md`

### **Fase 3: Migración de Funciones Legacy** (2-3 días)
1. ✅ Extraer prompts embebidos de `agno_prompts_new.py`
2. ✅ Crear archivos Markdown correspondientes
3. ✅ Actualizar `PromptService` para usar nuevos prompts
4. ✅ Eliminar `agno_prompts_new.py`

### **Fase 4: Integración y Testing** (2 días)
1. ✅ Actualizar `app.py` para usar `PromptService`
2. ✅ Tests de integración completos
3. ✅ Validación de todas las funcionalidades
4. ✅ Documentación de uso

---

## 🎨 Beneficios del Sistema Propuesto

### **Para Usuarios No Técnicos** 👥
- ✅ **Edición visual**: Prompts en Markdown fácil de leer y editar
- ✅ **Sin código**: Modificar prompts sin tocar archivos Python
- ✅ **Previsualización**: Ver cambios antes de aplicarlos
- ✅ **Ejemplos incluidos**: Cada prompt incluye ejemplos de uso

### **Para Desarrolladores** 🛠️
- ✅ **Separación de responsabilidades**: Prompts fuera del código
- ✅ **Versionado granular**: Git tracking por archivo de prompt
- ✅ **Testing individual**: Cada prompt es testeable independientemente
- ✅ **Cache automático**: Carga eficiente con cache en memoria

### **Para el Sistema** 🏗️
- ✅ **Escalabilidad**: Fácil agregar nuevos prompts y categorías
- ✅ **Mantenibilidad**: Estructura clara y organizada
- ✅ **Multiidioma**: Soporte nativo para múltiples idiomas
- ✅ **Validación**: Sistema robusto de validación automática

---

## 🚀 Implementación Detallada

### **Estructura de Archivos Markdown**

#### **Secciones Obligatorias**
```markdown
# [Título del Prompt]

## Purpose
[Descripción clara del propósito]

## Input Format
[Formato esperado de entrada]

## Output Format
[Formato esperado de salida]

## English Prompt
[Prompt en inglés]

## Spanish Prompt  
[Prompt en español]

## Variables
[Lista de variables con descripción]

## Examples
[Ejemplos de entrada y salida esperada]
```

#### **Convenciones de Variables**
- Variables en formato `{variable_name}`
- Nombres descriptivos en snake_case
- Documentación obligatoria de cada variable
- Ejemplos de valores para cada variable

### **Sistema de Validación**

#### **Validaciones Automáticas**
1. **Estructura**: Verificar secciones obligatorias
2. **Variables**: Confirmar que todas las variables están documentadas
3. **Idiomas**: Validar que existen traducciones para todos los idiomas soportados
4. **Sintaxis**: Verificar formato Markdown válido
5. **Metadatos**: Validar archivos JSON de metadatos

#### **Tests de Calidad**
```python
def test_prompt_completeness():
    """Verifica que todos los prompts tienen todas las secciones"""
    
def test_variable_consistency():
    """Verifica que las variables están presentes en todos los idiomas"""
    
def test_output_format():
    """Valida que los prompts generan salida en el formato esperado"""
```

### **Herramientas de Gestión**

#### **CLI para Gestión de Prompts** (`prompt_cli.py`)
```bash
# Crear nuevo prompt
python prompt_cli.py create --category user-story --id validate --languages en,es

# Validar todos los prompts  
python prompt_cli.py validate

# Listar prompts disponibles
python prompt_cli.py list

# Testear un prompt específico
python prompt_cli.py test --category user-story --id enhance --input "Login feature"
```

#### **Interface Web de Gestión** (Opcional)
- Dashboard para ver todos los prompts
- Editor WYSIWYG para Markdown
- Preview en tiempo real
- Sistema de validación visual

---

## 📊 Comparación: Antes vs Después

### **Sistema Actual** ❌
```python
# Prompts embebidos en código
def enhance_user_story(user_story: str) -> str:
    prompt = f"""Your task is to improve the following user story...
    {user_story}
    ..."""
    return llm.invoke(prompt).content
```

**Problemas**:
- Prompts mezclados con lógica
- Difícil de modificar sin programar
- Sin versionado granular
- Testing complejo

### **Sistema Propuesto** ✅
```python
# Servicio limpio que usa prompts externos
def enhance_user_story(user_story: str, language: str = "en") -> str:
    return self.prompt_service.execute_prompt(
        "user-story", "enhance", language, user_story=user_story
    )
```

**Beneficios**:
- Separación clara de responsabilidades
- Prompts editables sin código
- Versionado granular por archivo
- Testing individual de prompts
- Documentación incluida

---

## 🔍 Casos de Uso Detallados

### **Caso 1: Product Manager Mejora Prompts**
1. **Contexto**: PM identifica que el prompt de user stories no genera criterios de aceptación específicos
2. **Acción**: Edita `prompts/user-story/enhance.md` directamente en el editor
3. **Resultado**: Cambios aplicados inmediatamente sin intervención de desarrollo

### **Caso 2: QA Engineer Agrega Nuevos Casos**
1. **Contexto**: QA necesita nuevo prompt para testing de APIs
2. **Acción**: Crea `prompts/api-testing/endpoint-validation.md` siguiendo template
3. **Resultado**: Nuevo prompt disponible automáticamente en la aplicación

### **Caso 3: Developer Mantiene el Sistema**
1. **Contexto**: Developer necesita debugging de prompts
2. **Acción**: Ejecuta `python prompt_cli.py test --category user-story --id enhance`
3. **Resultado**: Validación completa y reportes de errores detallados

---

## 📋 Plan de Implementación Paso a Paso

### **Semana 1: Infraestructura** 
```bash
Día 1-2: Crear MarkdownPromptLoader y PromptMarkdownParser
Día 3: Implementar PromptValidator con tests
Día 4: Crear PromptService base
Día 5: Testing de infraestructura + Documentación
```

### **Semana 2: Migración Prompts Estructurados**
```bash
Día 1: Migrar user_story_prompts.py → Markdown
Día 2: Migrar test_case_prompts.py + gherkin_prompts.py
Día 3: Migrar code_gen_prompts.py (todos los frameworks)
Día 4: Migrar test_summarization_prompts.py
Día 5: Validación y tests de migración
```

### **Semana 3: Legacy + Funciones Avanzadas**
```bash
Día 1-2: Extraer prompts de agno_prompts_new.py
Día 3: Implementar CLI de gestión
Día 4: Migrar browser_prompts.py
Día 5: Testing final + Performance
```

### **Semana 4: Integración Final**
```bash
Día 1: Actualizar app.py para usar PromptService
Día 2: Tests de integración E2E
Día 3: Documentación completa
Día 4: Training del equipo
Día 5: Deploy y monitoreo
```

---

## ⚡ Acciones Inmediatas

### **Hoy (30 Mayo 2025)**
1. ✅ **Crear directorio**: `mkdir -p prompts/{user-story,test-cases,code-generation,test-analysis,browser-automation,shared}`
2. ✅ **Implementar parser básico**: Leer archivos Markdown y extraer secciones
3. ✅ **Crear primer prompt**: Migrar `user_story_enhance` como prueba de concepto

### **Esta Semana**
1. ✅ **Completar infraestructura base**
2. ✅ **Migrar 3-4 prompts más importantes**
3. ✅ **Implementar validación básica**
4. ✅ **Tests unitarios fundamentales**

### **Próximas 2 Semanas**
1. ✅ **Migración completa de prompts estructurados**
2. ✅ **Extracción de prompts legacy**
3. ✅ **Integración con sistema actual**

---

## 🎯 Criterios de Éxito

### **Técnicos**
- [ ] ✅ 100% de prompts migrados a Markdown
- [ ] ✅ 0 prompts embebidos en código Python
- [ ] ✅ Validación automática funcionando
- [ ] ✅ Cache de prompts implementado
- [ ] ✅ Tests de todos los prompts passing

### **Funcionales**
- [ ] ✅ Usuario no técnico puede editar prompts
- [ ] ✅ Cambios en prompts se reflejan sin restart
- [ ] ✅ Sistema mantiene performance actual
- [ ] ✅ Soporte multiidioma funcionando
- [ ] ✅ Ejemplos y documentación completa

### **Arquitectónicos**
- [ ] ✅ Separación completa prompts/código
- [ ] ✅ Sistema escalable para nuevos prompts
- [ ] ✅ Versionado granular implementado
- [ ] ✅ Rollback fácil en caso de problemas

---

## 🔒 Consideraciones de Seguridad

### **Validación de Input**
- Sanitización de variables antes de sustitución
- Validación de estructura de archivos Markdown
- Prevención de inyección de prompts maliciosos

### **Control de Acceso**
- Sistema de permisos para edición de prompts
- Log de cambios con autor y timestamp
- Backup automático antes de modificaciones

---

## 📈 ROI Esperado

### **Reducción de Tiempo**
- **70% menos tiempo** para modificar prompts (sin necesidad de developer)
- **50% menos tiempo** en testing de prompts (validación automática)
- **80% menos tiempo** en onboarding de nuevos prompts

### **Mejora de Calidad**
- **Validación automática** evita errores de formato
- **Documentación obligatoria** mejora comprensión
- **Ejemplos incluidos** facilitan uso correcto

### **Escalabilidad**
- **Sistema modular** permite crecimiento sin deuda técnica
- **Arquitectura limpia** facilita mantenimiento futuro
- **Separación de responsabilidades** mejora colaboración entre equipos

---

*Plan generado el 30 de mayo de 2025*  
*Arquitecto de Software: GitHub Copilot*
