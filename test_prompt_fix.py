#!/usr/bin/env python3
"""
Test script to verify the prompt service fixes
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_prompt_loading():
    """Test if the prompt can be loaded without errors"""
    try:
        from src.Core.markdown_prompt_loader import MarkdownPromptLoader
        
        print("Testing MarkdownPromptLoader...")
        loader = MarkdownPromptLoader()
        
        # Test loading English prompt
        print("Loading English prompt...")
        en_result = loader.load_prompt('browser-automation', 'task-generation', 'en')
        print(f"✅ English prompt loaded successfully ({len(en_result)} characters)")
        
        # Test loading Spanish prompt
        print("Loading Spanish prompt...")
        es_result = loader.load_prompt('browser-automation', 'task-generation', 'es')
        print(f"✅ Spanish prompt loaded successfully ({len(es_result)} characters)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading prompts: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompt_service():
    """Test if the prompt service can generate tasks without errors"""
    try:
        from src.Core.prompt_service import PromptService
        
        print("\nTesting PromptService...")
        
        # Mock the LLM to avoid API calls
        class MockLLM:
            def invoke(self, prompt):
                class MockResponse:
                    content = "Mock response for testing"
                return MockResponse()
        
        ps = PromptService()
        ps.llm = MockLLM()  # Replace with mock
        
        # Test with all required parameters
        print("Testing browser task generation...")
        result = ps.generate_browser_task(
            scenario="Given I visit Google",
            language="es",
            url_preservation_instructions="Test URL instructions"
        )
        print(f"✅ Browser task generated successfully: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error with PromptService: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Testing prompt service fixes...\n")
    
    success = True
    
    # Test 1: Prompt loading
    if not test_prompt_loading():
        success = False
    
    # Test 2: Prompt service
    if not test_prompt_service():
        success = False
    
    print("\n" + "="*50)
    if success:
        print("🎉 All tests passed! The fixes appear to be working.")
    else:
        print("❌ Some tests failed. There may still be issues.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
