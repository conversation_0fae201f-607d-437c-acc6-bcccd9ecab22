"""
Test the new Markdown-based Prompt System
Simple test to verify the basic functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.Core.markdown_prompt_loader import Markdown<PERSON>romptLoader
from src.Core.prompt_markdown_parser import PromptMarkdownParser
from src.Core.prompt_service import PromptService

def test_basic_functionality():
    """Test basic functionality of the new prompt system."""
    
    print("🧪 Testing Markdown Prompt System...")
    
    try:
        # Test 1: Initialize loader
        print("\n1️⃣ Testing MarkdownPromptLoader...")
        loader = MarkdownPromptLoader("prompts")
        print("✅ Loader initialized successfully")
        
        # Test 2: Load available prompts
        print("\n2️⃣ Testing available prompts...")
        available = loader.get_available_prompts()
        print(f"✅ Found prompts: {available}")
        
        # Test 3: Load specific prompt
        print("\n3️⃣ Testing prompt loading...")
        if "user-story" in available and "enhance" in available.get("user-story", []):
            prompt_en = loader.load_prompt("user-story", "enhance", "en")
            print(f"✅ English prompt loaded (length: {len(prompt_en)} chars)")
            
            prompt_es = loader.load_prompt("user-story", "enhance", "es")
            print(f"✅ Spanish prompt loaded (length: {len(prompt_es)} chars)")
        else:
            print("⚠️ No user-story/enhance prompt found")
        
        # Test 4: Test parser
        print("\n4️⃣ Testing PromptMarkdownParser...")
        parser = PromptMarkdownParser()
        variables = parser.extract_variables(prompt_en)
        print(f"✅ Variables extracted: {variables}")
        
        # Test 5: Test variable substitution
        print("\n5️⃣ Testing variable substitution...")
        test_prompt = "Hello {user_story}, this is a {test_var}!"
        substituted = parser.substitute_variables(test_prompt, user_story="world", test_var="success")
        print(f"✅ Substituted: {substituted}")
        
        # Test 6: Test PromptService (without LLM call)
        print("\n6️⃣ Testing PromptService...")
        # Don't test LLM call in basic test to avoid API costs
        service = PromptService("prompts")
        print("✅ PromptService initialized successfully")
        
        available_service = service.get_available_prompts()
        print(f"✅ Service found prompts: {available_service}")
        
        validation_results = service.validate_all_prompts()
        print(f"✅ Validation results: {validation_results}")
        
        print("\n🎉 All tests passed! The Markdown Prompt System is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    sys.exit(0 if success else 1)
