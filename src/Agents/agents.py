"""<PERSON><PERSON><PERSON><PERSON> que contiene agentes especializados para diferentes tareas."""

from typing import Dict, Any, Optional, List, Literal
from langchain_google_genai import ChatGoogleGenerativeAI
from browser_use import Browser, Agent as BrowserAgent
import os
from dotenv import load_dotenv

from src.Utilities.utils import controller
from src.Core.prompt_service import PromptService
from typing import Literal
LanguageType = Literal["en", "es"]
from src.Utilities.browser_helper import (
    create_and_run_agent,
    create_robust_config
)

# Load environment variables
load_dotenv()

class StoryAgent:
    """Agente para mejorar historias de usuario y generar escenarios de prueba."""

    def __init__(self, api_key: str, language: Optional[LanguageType] = None):
        """Inicializa el agente de historias.

        Args:
            api_key: API key para el LLM
            language: Idioma de las respuestas ('en' o 'es')
        """
        self.api_key = api_key
        self.llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=api_key)
        self.language = language or os.getenv("PROMPT_LANGUAGE", "en")
        self.prompt_service = PromptService()

    def enhance_story(self, user_story: str) -> str:
        """Mejora una historia de usuario.

        Args:
            user_story: Historia de usuario original

        Returns:
            str: Historia de usuario mejorada
        """
        # Use the new prompt service
        enhanced_story = self.prompt_service.enhance_user_story(user_story, self.language)
        return enhanced_story

    def generate_manual_tests(self, enhanced_story: str) -> str:
        """Genera casos de prueba manuales a partir de una historia mejorada.

        Args:
            enhanced_story: Historia de usuario mejorada

        Returns:
            str: Casos de prueba manuales en formato markdown
        """
        # Use the new prompt service
        manual_tests = self.prompt_service.generate_manual_test_cases(enhanced_story, self.language)
        return manual_tests

    def generate_gherkin(self, manual_tests: str) -> str:
        """Genera escenarios Gherkin a partir de casos de prueba manuales.

        Args:
            manual_tests: Casos de prueba manuales

        Returns:
            str: Escenarios Gherkin
        """
        # Use the new prompt service
        gherkin = self.prompt_service.convert_to_gherkin(manual_tests, self.language)
        return gherkin

class BrowserAutomationAgent:
    """Agente para automatizar interacciones con navegadores."""

    def __init__(self, api_key: str, language: Optional[LanguageType] = None):
        """Inicializa el agente de automatización de navegador.

        Args:
            api_key: API key para el LLM
            language: Idioma de las respuestas ('en' o 'es')
        """
        self.api_key = api_key
        self.llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=api_key)
        self.language = language or os.getenv("PROMPT_LANGUAGE", "en")

    async def execute_scenario(self, scenario: str) -> Dict[str, Any]:
        """Ejecuta un escenario Gherkin en un navegador.

        Args:
            scenario: Escenario Gherkin a ejecutar

        Returns:
            Dict[str, Any]: Resultado de la ejecucion
        """
        # Crear configuración optimizada para agentes de automatización
        agent_config = create_robust_config(
            headless=True,  # Headless para agentes automatizados
            use_vision=True,  # Visión completa para mejor análisis
            max_steps=100,  # Pasos adecuados para escenarios típicos
            minimum_wait_page_load_time=0.5,
            wait_for_network_idle_page_load_time=1.0,
            maximum_wait_page_load_time=10.0,
            wait_between_actions=0.5,
            viewport_expansion=500,  # Contexto visual balanceado
            deterministic_rendering=True,  # Renderizado consistente
            highlight_elements=True  # Mantener highlighting para debugging
        )

        # Utilizar la función helper mejorada para crear y ejecutar el agente
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,  # controller se importa globalmente
            api_key=self.api_key,  # Pasar la api_key de la instancia
            language=self.language,  # Pasar el idioma de la instancia
            config=agent_config  # Usar configuración optimizada
        )

        return {
            "history": history,
            "success": True,  # Asumir éxito si no hay excepciones
            "final_result": history.final_result() if hasattr(history, 'final_result') else None
        }