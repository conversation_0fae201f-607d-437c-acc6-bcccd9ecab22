"""
Enhanced Prompt Service for AgentQA
Unified service for executing prompts from the Markdown-based system
"""

import os
import json
from typing import Dict, Any, Optional
from langchain_google_genai import ChatGoogleGenerativeAI
from src.Core.markdown_prompt_loader import <PERSON>down<PERSON>romptLoader
from src.Core.prompt_markdown_parser import PromptMarkdownParser
import logging

logger = logging.getLogger(__name__)

class PromptService:
    """Enhanced service for managing and executing prompts."""

    def __init__(self, prompts_dir: str = "prompts"):
        """Initialize the prompt service.

        Args:
            prompts_dir: Directory containing prompt markdown files
        """
        self.prompts_dir = prompts_dir
        self.loader = MarkdownPromptLoader(prompts_dir)
        self.parser = PromptMarkdownParser()

        # Initialize LLM
        self.llm = ChatGoogleGenerativeAI(
            model=os.getenv("LLM_MODEL", "gemini-2.0-flash"),
            api_key=os.environ.get("GOOGLE_API_KEY")
        )

    def execute_prompt(self, category: str, prompt_id: str, language: str = "en", **variables) -> str:
        """Execute a prompt with the provided variables.

        Args:
            category: Prompt category (e.g., 'user-story', 'test-cases')
            prompt_id: Prompt identifier (e.g., 'enhance', 'generate')
            language: Language code ('en' or 'es')
            **variables: Variables to substitute in the prompt

        Returns:
            str: The LLM response

        Raises:
            ValueError: If prompt is not found or variables are missing
        """
        try:
            # Load the prompt template
            prompt_template = self.loader.load_prompt(category, prompt_id, language)

            # Substitute variables
            prompt_text = self.parser.substitute_variables(prompt_template, **variables)

            # Execute with LLM
            response = self.llm.invoke(prompt_text)

            return response.content

        except Exception as e:
            logger.error(f"Error executing prompt {category}:{prompt_id}: {e}")
            raise

    def substitute_template(self, category: str, prompt_id: str, language: str = "en", **variables) -> str:
        """Substitute variables in a prompt template without LLM execution.

        Args:
            category: Prompt category (e.g., 'browser-automation')
            prompt_id: Prompt identifier (e.g., 'task-generation')
            language: Language code ('en' or 'es')
            **variables: Variables to substitute in the prompt

        Returns:
            str: The template with variables substituted (no LLM processing)

        Raises:
            ValueError: If prompt is not found or variables are missing
        """
        try:
            # Load the prompt template
            prompt_template = self.loader.load_prompt(category, prompt_id, language)

            # Substitute variables and return directly (no LLM execution)
            return self.parser.substitute_variables(prompt_template, **variables)

        except Exception as e:
            logger.error(f"Error substituting template {category}:{prompt_id}: {e}")
            raise

    # Convenience methods for each prompt type

    def enhance_user_story(self, user_story: str, language: str = "en") -> str:
        """Enhance a user story to follow standard format.

        Args:
            user_story: Original user story text
            language: Language for the prompt ('en' or 'es')

        Returns:
            Enhanced user story text
        """
        return self.execute_prompt("user-story", "enhance", language, user_story=user_story)

    def generate_test_cases(self, user_story: str, language: str = "en") -> str:
        """Generate manual test cases from a user story.

        Args:
            user_story: User story text
            language: Language for the prompt ('en' or 'es')

        Returns:
            Generated test cases
        """
        return self.execute_prompt("test-cases", "manual-generation", language, user_story=user_story)

    def generate_gherkin(self, test_cases: str, language: str = "en") -> str:
        """Convert test cases to Gherkin format.

        Args:
            test_cases: Manual test cases text
            language: Language for the prompt ('en' or 'es')

        Returns:
            Gherkin scenarios
        """
        return self.execute_prompt("test-cases", "gherkin-conversion", language, manual_test_cases=test_cases)

    def generate_code(self, framework: str, gherkin_scenario: str, history: Dict[str, Any], language: str = "en") -> str:
        """Generate automation code for a specific framework.

        Args:
            framework: Framework name (e.g., 'selenium-pytest', 'cypress', 'playwright')
            gherkin_scenario: Gherkin scenario text
            history: Execution history with context
            language: Language for the prompt ('en' or 'es')

        Returns:
            Generated automation code
        """
        # Extract context from history
        base_url = history.get("urls", [""])[0] if history.get("urls") else ""
        selectors = history.get("element_xpaths", {})
        actions = history.get("action_names", [])
        extracted_content = history.get("extracted_content", [])

        return self.execute_prompt(
            "code-generation",
            framework,
            language,
            gherkin_steps=gherkin_scenario,
            base_url=base_url,
            selectors=json.dumps(selectors, indent=2),
            actions=json.dumps(actions, indent=2),
            extracted_content=json.dumps(extracted_content, indent=2)
        )

    def generate_browser_task(self, scenario: str, language: str = "en", **context) -> str:
        """Generate browser automation task.

        Args:
            scenario: Scenario description
            language: Language for the prompt ('en' or 'es')
            **context: Additional context variables

        Returns:
            Browser task instructions (template with variables substituted, NOT LLM response)
        """
        # For browser automation, we don't want LLM processing - just template substitution
        return self.substitute_template("browser-automation", "task-generation", language, scenario=scenario, **context)

    def summarize_test_results(self, test_results: str, language: str = "en") -> str:
        """Summarize test execution results.

        Args:
            test_results: Raw test results data
            language: Language for the prompt ('en' or 'es')

        Returns:
            Summarized test results
        """
        return self.execute_prompt("test-analysis", "results-summary", language, test_results=test_results)

    # Utility methods

    def get_available_prompts(self) -> Dict[str, list]:
        """Get all available prompts by category.

        Returns:
            Dict mapping categories to prompt lists
        """
        return self.loader.get_available_prompts()

    def validate_all_prompts(self) -> Dict[str, bool]:
        """Validate all available prompts.

        Returns:
            Dict mapping prompt paths to validation status
        """
        results = {}
        available_prompts = self.get_available_prompts()

        for category, prompt_ids in available_prompts.items():
            for prompt_id in prompt_ids:
                key = f"{category}:{prompt_id}"
                results[key] = self.loader.validate_prompt(category, prompt_id)

        return results

    def clear_cache(self) -> None:
        """Clear the prompt cache."""
        self.loader.clear_cache()

    def get_prompt_info(self, category: str, prompt_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific prompt.

        Args:
            category: Prompt category
            prompt_id: Prompt identifier

        Returns:
            Dict with prompt information or None if not found
        """
        try:
            metadata = self.loader.load_metadata(category)
            for prompt in metadata.get("prompts", []):
                if prompt["id"] == prompt_id:
                    # Add runtime information
                    prompt["variables"] = self.loader.get_prompt_variables(category, prompt_id)
                    return prompt
            return None
        except Exception as e:
            logger.error(f"Error getting prompt info for {category}:{prompt_id}: {e}")
            return None
