import os
import json
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional, Union


class TestCase:
    """
    Representa un caso de prueba individual dentro de una suite de pruebas.
    """
    def __init__(self,
                 name: str,
                 description: str = "",
                 instrucciones: str = "",
                 historia_de_usuario: str = "",
                 gherkin: str = "",
                 url: str = "",
                 tags: List[str] = None,
                 test_id: str = None):
        self.test_id = test_id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.instrucciones = instrucciones
        self.historia_de_usuario = historia_de_usuario
        self.gherkin = gherkin
        self.url = url
        self.tags = tags or []
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        self.history_files = []  # Lista de archivos JSON de historial de ejecución
        self.status = "Not Executed"  # Not Executed, Passed, Failed, Blocked
        self.last_execution = None
        self.code = ""  # Código de automatización generado
        self.framework = ""  # Framework utilizado para la automatización

    def to_dict(self) -> Dict[str, Any]:
        """Convierte el caso de prueba a un diccionario para serialización."""
        return {
            "test_id": self.test_id,
            "name": self.name,
            "description": self.description,
            "instrucciones": self.instrucciones,
            "historia_de_usuario": self.historia_de_usuario,
            "gherkin": self.gherkin,
            "url": self.url,
            "tags": self.tags,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "history_files": self.history_files,
            "status": self.status,
            "last_execution": self.last_execution,
            "code": self.code,
            "framework": self.framework
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestCase':
        """Crea un caso de prueba a partir de un diccionario."""
        # Compatibilidad hacia atrás: convertir steps y expected_results a instrucciones e historia_de_usuario
        instrucciones = data.get("instrucciones", "")
        historia_de_usuario = data.get("historia_de_usuario", "")

        # Si no hay instrucciones pero hay steps, convertir steps a instrucciones
        if not instrucciones and "steps" in data:
            steps = data.get("steps", [])
            if steps:
                instrucciones = "\n".join([f"{i+1}. {step}" for i, step in enumerate(steps)])

        # Si no hay historia_de_usuario pero hay expected_results, convertir a historia_de_usuario
        if not historia_de_usuario and "expected_results" in data:
            expected_results = data.get("expected_results", [])
            if expected_results:
                historia_de_usuario = "Resultados esperados:\n" + "\n".join([f"- {result}" for result in expected_results])

        test_case = cls(
            name=data["name"],
            description=data.get("description", ""),
            instrucciones=instrucciones,
            historia_de_usuario=historia_de_usuario,
            gherkin=data.get("gherkin", ""),
            url=data.get("url", ""),
            tags=data.get("tags", []),
            test_id=data.get("test_id")
        )
        test_case.created_at = data.get("created_at", test_case.created_at)
        test_case.updated_at = data.get("updated_at", test_case.updated_at)
        test_case.history_files = data.get("history_files", [])
        test_case.status = data.get("status", "Not Executed")
        test_case.last_execution = data.get("last_execution")
        test_case.code = data.get("code", "")
        test_case.framework = data.get("framework", "")
        return test_case

    def add_history_file(self, file_path: str) -> None:
        """Añade un archivo de historial de ejecución al caso de prueba."""
        if file_path not in self.history_files:
            self.history_files.append(file_path)
            self.updated_at = datetime.now().isoformat()

    def update_status(self, status: str, execution_time: str = None) -> None:
        """Actualiza el estado del caso de prueba."""
        self.status = status
        self.last_execution = execution_time or datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()


class TestSuite:
    """
    Representa una suite de pruebas que contiene múltiples casos de prueba.
    """
    def __init__(self,
                 name: str,
                 description: str = "",
                 tags: List[str] = None,
                 suite_id: str = None):
        self.suite_id = suite_id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.tags = tags or []
        self.test_cases = {}  # Dict[test_id, TestCase]
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at

    def to_dict(self) -> Dict[str, Any]:
        """Convierte la suite de pruebas a un diccionario para serialización."""
        return {
            "suite_id": self.suite_id,
            "name": self.name,
            "description": self.description,
            "tags": self.tags,
            "test_cases": {test_id: test_case.to_dict() for test_id, test_case in self.test_cases.items()},
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestSuite':
        """Crea una suite de pruebas a partir de un diccionario."""
        suite = cls(
            name=data["name"],
            description=data.get("description", ""),
            tags=data.get("tags", []),
            suite_id=data.get("suite_id")
        )
        suite.created_at = data.get("created_at", suite.created_at)
        suite.updated_at = data.get("updated_at", suite.updated_at)

        # Cargar casos de prueba
        test_cases_data = data.get("test_cases", {})
        for test_id, test_case_data in test_cases_data.items():
            suite.test_cases[test_id] = TestCase.from_dict(test_case_data)

        return suite

    def add_test_case(self, test_case: TestCase) -> None:
        """Añade un caso de prueba a la suite."""
        self.test_cases[test_case.test_id] = test_case
        self.updated_at = datetime.now().isoformat()

    def remove_test_case(self, test_id: str) -> bool:
        """Elimina un caso de prueba de la suite."""
        if test_id in self.test_cases:
            del self.test_cases[test_id]
            self.updated_at = datetime.now().isoformat()
            return True
        return False

    def get_test_case(self, test_id: str) -> Optional[TestCase]:
        """Obtiene un caso de prueba por su ID."""
        return self.test_cases.get(test_id)

    def get_all_test_cases(self) -> List[TestCase]:
        """Obtiene todos los casos de prueba de la suite."""
        return list(self.test_cases.values())


class Project:
    """
    Representa un proyecto de pruebas que contiene múltiples suites de pruebas.
    """
    def __init__(self,
                 name: str,
                 description: str = "",
                 tags: List[str] = None,
                 project_id: str = None):
        self.project_id = project_id or str(uuid.uuid4())
        self.name = name
        self.description = description
        self.tags = tags or []
        self.test_suites = {}  # Dict[suite_id, TestSuite]
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at

    def to_dict(self) -> Dict[str, Any]:
        """Convierte el proyecto a un diccionario para serialización."""
        return {
            "project_id": self.project_id,
            "name": self.name,
            "description": self.description,
            "tags": self.tags,
            "test_suites": {suite_id: suite.to_dict() for suite_id, suite in self.test_suites.items()},
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Project':
        """Crea un proyecto a partir de un diccionario."""
        project = cls(
            name=data["name"],
            description=data.get("description", ""),
            tags=data.get("tags", []),
            project_id=data.get("project_id")
        )
        project.created_at = data.get("created_at", project.created_at)
        project.updated_at = data.get("updated_at", project.updated_at)

        # Cargar suites de pruebas
        test_suites_data = data.get("test_suites", {})
        for suite_id, suite_data in test_suites_data.items():
            project.test_suites[suite_id] = TestSuite.from_dict(suite_data)

        return project

    def add_test_suite(self, test_suite: TestSuite) -> None:
        """Añade una suite de pruebas al proyecto."""
        self.test_suites[test_suite.suite_id] = test_suite
        self.updated_at = datetime.now().isoformat()

    def remove_test_suite(self, suite_id: str) -> bool:
        """Elimina una suite de pruebas del proyecto."""
        if suite_id in self.test_suites:
            del self.test_suites[suite_id]
            self.updated_at = datetime.now().isoformat()
            return True
        return False

    def get_test_suite(self, suite_id: str) -> Optional[TestSuite]:
        """Obtiene una suite de pruebas por su ID."""
        return self.test_suites.get(suite_id)

    def get_all_test_suites(self) -> List[TestSuite]:
        """Obtiene todas las suites de pruebas del proyecto."""
        return list(self.test_suites.values())
