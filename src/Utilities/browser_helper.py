import os
import logging
import re
from typing import Optional, Any, List, Union, Dict
from pathlib import Path
from datetime import datetime

# Browser-use imports with proper error handling
try:
    from browser_use import Agent as BrowserAgent, BrowserSession, BrowserProfile
    from browser_use.agent.memory import MemoryConfig
    BROWSER_USE_AVAILABLE = True
    MEMORY_CONFIG_AVAILABLE = True
except ImportError as e:
    logging.error(f"Failed to import browser-use components: {e}")
    BROWSER_USE_AVAILABLE = False
    MEMORY_CONFIG_AVAILABLE = False

# LLM imports
from langchain_google_genai import ChatGoogleGenerativeAI

# Optional LLM imports
try:
    from langchain_openai import ChatOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from langchain_anthropic import ChatAnthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

# Import prompt service
from src.Core.prompt_service import PromptService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BrowserHelperConfig:
    """Enhanced configuration class for browser helper settings."""

    def __init__(
        self,
        # Browser settings
        headless: Optional[bool] = None,
        user_data_dir: Optional[str] = None,
        allowed_domains: Optional[List[str]] = None,
        disable_security: bool = False,
        deterministic_rendering: bool = True,
        highlight_elements: bool = True,
        viewport_expansion: int = 500,

        # Performance settings
        minimum_wait_page_load_time: float = 0.5,
        wait_for_network_idle_page_load_time: float = 1.0,
        maximum_wait_page_load_time: float = 10.0,
        wait_between_actions: float = 0.5,

        # Agent settings
        max_steps: int = 100,
        use_vision: bool = True,
        enable_memory: bool = False,
        save_conversation_path: Optional[str] = None,

        # Browser-use memory settings (MemoryConfig compatible)
        memory_agent_id: Optional[str] = None,
        memory_interval: int = 10,

        # Planner settings (browser-use 0.2.5+ compatible)
        planner_llm: Optional[Any] = None,
        use_vision_for_planner: bool = True,
        planner_interval: int = 1,

        # Initial actions for optimization
        initial_actions: Optional[List[Dict[str, Any]]] = None,

        # Browser session settings
        keep_alive: bool = False,
        storage_state: Optional[str] = None,

        # Model settings
        model_provider: str = "gemini",
        model_name: Optional[str] = None,
        temperature: float = 0.0,

        # Viewport settings
        viewport: Optional[Dict[str, int]] = None,
        device_scale_factor: Optional[float] = None,

        # Recording settings
        record_video_dir: Optional[str] = None,
        trace_path: Optional[str] = None,

        # Additional kwargs for future compatibility
        **kwargs
    ):
        # Browser settings
        self.headless = headless
        self.user_data_dir = user_data_dir
        self.allowed_domains = allowed_domains
        self.disable_security = disable_security
        self.deterministic_rendering = deterministic_rendering
        self.highlight_elements = highlight_elements
        self.viewport_expansion = viewport_expansion

        # Performance settings
        self.minimum_wait_page_load_time = minimum_wait_page_load_time
        self.wait_for_network_idle_page_load_time = wait_for_network_idle_page_load_time
        self.maximum_wait_page_load_time = maximum_wait_page_load_time
        self.wait_between_actions = wait_between_actions

        # Agent settings
        self.max_steps = max_steps
        self.use_vision = use_vision
        self.enable_memory = enable_memory
        self.save_conversation_path = save_conversation_path

        # Browser-use memory settings
        self.memory_agent_id = memory_agent_id
        self.memory_interval = memory_interval

        # Planner settings
        self.planner_llm = planner_llm
        self.use_vision_for_planner = use_vision_for_planner
        self.planner_interval = planner_interval

        # Initial actions
        self.initial_actions = initial_actions

        # Browser session settings
        self.keep_alive = keep_alive
        self.storage_state = storage_state

        # Model settings
        self.model_provider = model_provider
        self.model_name = model_name
        self.temperature = temperature

        # Viewport settings
        self.viewport = viewport
        self.device_scale_factor = device_scale_factor

        # Recording settings
        self.record_video_dir = record_video_dir
        self.trace_path = trace_path

        # Store any additional parameters for future compatibility
        for key, value in kwargs.items():
            setattr(self, key, value)


def create_llm_instance(config: BrowserHelperConfig, api_key: str):
    """Create LLM instance based on provider configuration."""

    if config.model_provider.lower() == "gemini":
        model_name = config.model_name or os.getenv("LLM_MODEL", "gemini-2.0-flash")
        return ChatGoogleGenerativeAI(
            model=model_name,
            api_key=api_key,
            temperature=config.temperature
        )

    elif config.model_provider.lower() == "openai":
        if not OPENAI_AVAILABLE:
            raise ValueError("OpenAI not available. Install langchain-openai: pip install langchain-openai")

        model_name = config.model_name or "gpt-4o"
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable not found")
        return ChatOpenAI(
            model=model_name,
            api_key=openai_api_key,
            temperature=config.temperature
        )

    elif config.model_provider.lower() == "anthropic":
        if not ANTHROPIC_AVAILABLE:
            raise ValueError("Anthropic not available. Install langchain-anthropic: pip install langchain-anthropic")

        model_name = config.model_name or "claude-3-5-sonnet-20240620"
        anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
        if not anthropic_api_key:
            raise ValueError("ANTHROPIC_API_KEY environment variable not found")
        return ChatAnthropic(
            model_name=model_name,
            api_key=anthropic_api_key,
            temperature=config.temperature
        )

    else:
        raise ValueError(f"Unsupported model provider: {config.model_provider}")


def create_browser_profile(config: BrowserHelperConfig) -> BrowserProfile:
    """Create BrowserProfile from configuration."""

    if not BROWSER_USE_AVAILABLE:
        raise RuntimeError("browser-use not available. Please install browser-use>=0.2.4")

    # Auto-detect headless mode if not specified
    headless = config.headless
    if headless is None:
        headless = os.getenv("DISPLAY") is None and os.getenv("WAYLAND_DISPLAY") is None

    # Set default user data directory if not provided
    user_data_dir = config.user_data_dir
    if user_data_dir is None:
        user_data_dir = "~/.config/browseruse/profiles/agentqa_profile"

    # Build BrowserProfile arguments
    profile_args = {
        "headless": headless,
        "user_data_dir": user_data_dir,
        "allowed_domains": config.allowed_domains,
        "disable_security": config.disable_security,
        "deterministic_rendering": config.deterministic_rendering,
        "highlight_elements": config.highlight_elements,
        "viewport_expansion": config.viewport_expansion,
        "minimum_wait_page_load_time": config.minimum_wait_page_load_time,
        "wait_for_network_idle_page_load_time": config.wait_for_network_idle_page_load_time,
        "maximum_wait_page_load_time": config.maximum_wait_page_load_time,
        "wait_between_actions": config.wait_between_actions,
        "keep_alive": config.keep_alive,
    }

    # Add optional parameters if they exist
    if config.storage_state:
        profile_args["storage_state"] = config.storage_state

    if config.viewport:
        profile_args["viewport"] = config.viewport

    if config.device_scale_factor:
        profile_args["device_scale_factor"] = config.device_scale_factor

    if config.record_video_dir:
        profile_args["record_video_dir"] = config.record_video_dir

    if config.trace_path:
        profile_args["trace_path"] = config.trace_path

    # Remove None values
    profile_args = {k: v for k, v in profile_args.items() if v is not None}

    return BrowserProfile(**profile_args)


def extract_urls_from_scenario(scenario_text: str) -> List[str]:
    """Extract URLs from Gherkin scenario text."""
    url_pattern = re.compile(
        r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[-\w%!$&\'()*+,;=:@/~]+)*(?:\?[-\w%!$&\'()*+,;=:@/~]*)?(?:#[-\w%!$&\'()*+,;=:@/~]*)?'
    )
    return url_pattern.findall(scenario_text)


def create_initial_actions_from_url(url: str) -> List[Dict[str, Any]]:
    """Create initial actions to navigate to a URL."""
    if not url:
        return []

    return [
        {'open_tab': {'url': url}}
    ]


async def create_and_run_agent(
    scenario_text: str,
    controller_instance,
    api_key: str = None,
    language: Optional[str] = None,
    config: Optional[BrowserHelperConfig] = None,
    url: Optional[str] = None,  # URL parameter for direct navigation
    # Legacy parameters for backward compatibility
    token_optimized: bool = False  # Ignored - kept for compatibility
) -> Any:
    """
    Enhanced browser agent creation and execution with comprehensive configuration.

    Args:
        scenario_text: The Gherkin scenario to execute
        controller_instance: Browser controller instance
        api_key: API key for the LLM (defaults to environment variable)
        language: Language for prompts ('en' or 'es')
        config: BrowserHelperConfig instance for advanced configuration
        url: URL for direct navigation (takes precedence over URL extraction from scenario)
        token_optimized: Legacy parameter (ignored)

    Returns:
        Agent execution history

    Raises:
        ValueError: If required API keys are missing
        Exception: If agent execution fails
    """

    if not BROWSER_USE_AVAILABLE:
        raise RuntimeError("browser-use not available. Please install browser-use>=0.2.4")

    # Use default config if none provided
    if config is None:
        config = BrowserHelperConfig()

    # Validate and set API key
    if api_key is None:
        if config.model_provider.lower() == "gemini":
            api_key = os.environ.get("GOOGLE_API_KEY")
            if not api_key:
                raise ValueError("GOOGLE_API_KEY environment variable not found. Please set it or pass an api_key.")
        elif config.model_provider.lower() == "openai":
            api_key = os.environ.get("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable not found.")
        elif config.model_provider.lower() == "anthropic":
            api_key = os.environ.get("ANTHROPIC_API_KEY")
            if not api_key:
                raise ValueError("ANTHROPIC_API_KEY environment variable not found.")

    # Determine effective language
    effective_language = language if language is not None else os.getenv("PROMPT_LANGUAGE", "en")

    # Generate agent task with enhanced prompts using PromptService
    prompt_service = PromptService()

    # Generate URL preservation instructions
    import re
    url_pattern = re.compile(
        r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[-\w%!$&\'()*+,;=:@/~]+)*(?:\?[-\w%!$&\'()*+,;=:@/~]*)?(?:#[-\w%!$&\'()*+,;=:@/~]*)?'
    )
    scenario_urls = url_pattern.findall(scenario_text)

    # Add URL parameter if provided
    all_urls = list(scenario_urls)
    if url and url not in all_urls:
        all_urls.append(url)

    # Create URL preservation instructions
    if all_urls:
        if effective_language == "es":
            url_preservation_instructions = """
**INSTRUCCIÓN CRÍTICA PARA PRESERVACIÓN DE URLs:**
DEBES usar las URLs exactas como se proporcionan en el escenario sin ninguna modificación. No cambies, abrevies u omitas ninguna parte de las URLs.
Las siguientes URLs deben preservarse exactamente como están escritas:
"""
        else:
            url_preservation_instructions = """
**CRITICAL URL PRESERVATION INSTRUCTION:**
You MUST use the exact URLs as provided in the scenario without any modification. Do not change, abbreviate, or omit any part of the URLs.
The following URLs must be preserved exactly as written:
"""
        for url_item in all_urls:
            url_preservation_instructions += f"    - {url_item}\n"
    else:
        url_preservation_instructions = ""

    agent_task = prompt_service.generate_browser_task(
        scenario_text,
        effective_language,
        url_preservation_instructions=url_preservation_instructions
    )

    logger.info(f"Creating browser agent with provider: {config.model_provider}")

    try:
        # Create LLM instance
        llm_instance = create_llm_instance(config, api_key)

        # Create browser profile
        browser_profile = create_browser_profile(config)

        # Create browser session
        browser_session = BrowserSession(
            browser_profile=browser_profile,
            keep_alive=config.keep_alive
        )

        # Set up conversation saving if requested
        conversation_path = None
        if config.save_conversation_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            conversation_path = f"{config.save_conversation_path}/conversation_{timestamp}.json"
            os.makedirs(os.path.dirname(conversation_path), exist_ok=True)

        # Create initial actions based on URL parameter or extract from scenario
        initial_actions = config.initial_actions
        if initial_actions is None:
            # Priority: 1) URL parameter, 2) Extract from scenario
            target_url = url
            if not target_url:
                urls = extract_urls_from_scenario(scenario_text)
                target_url = urls[0] if urls else None

            if target_url:
                initial_actions = create_initial_actions_from_url(target_url)
                logger.info(f"Auto-generated initial actions for URL: {target_url}")
            else:
                logger.info("No URL provided or found in scenario - no initial actions generated")

        # Configure browser agent with enhanced settings
        agent_kwargs = {
            "task": agent_task,
            "llm": llm_instance,
            "controller": controller_instance,
            "browser_session": browser_session,
            "use_vision": config.use_vision,
        }

        # Configure browser-use memory if enabled
        if config.enable_memory and MEMORY_CONFIG_AVAILABLE:
            memory_config = MemoryConfig(
                llm_instance=llm_instance,  # Use the same LLM for memory operations
                agent_id=config.memory_agent_id or "browser_use_agent",
                memory_interval=config.memory_interval
            )
            agent_kwargs["enable_memory"] = True
            agent_kwargs["memory_config"] = memory_config
            logger.info(f"Browser-use memory enabled with agent_id: {memory_config.agent_id}")
        else:
            agent_kwargs["enable_memory"] = False
            if config.enable_memory and not MEMORY_CONFIG_AVAILABLE:
                logger.warning("Memory requested but MemoryConfig not available")

        # Add planner configuration if planner_llm is provided
        if config.planner_llm:
            agent_kwargs["planner_llm"] = config.planner_llm
            agent_kwargs["use_vision_for_planner"] = config.use_vision_for_planner
            agent_kwargs["planner_interval"] = config.planner_interval
            logger.info("Planner enabled with custom LLM")

        # Add initial actions if available
        if initial_actions:
            agent_kwargs["initial_actions"] = initial_actions
            logger.info(f"Using {len(initial_actions)} initial actions")

        # Add conversation saving if configured
        if conversation_path:
            agent_kwargs["save_conversation_path"] = conversation_path

        # Create the agent
        browser_agent_instance = BrowserAgent(**agent_kwargs)

        logger.info("Starting browser agent execution...")
        logger.info(f"Using model provider: {config.model_provider}")
        logger.info(f"Vision enabled: {config.use_vision}")
        logger.info(f"Max steps: {config.max_steps}")

        # Execute the agent
        history = await browser_agent_instance.run(max_steps=config.max_steps)

        logger.info("Browser agent execution completed successfully")

        # Log execution summary with safe attribute access
        try:
            if hasattr(history, 'action_names') and callable(getattr(history, 'action_names')):
                logger.info(f"Executed {len(history.action_names())} actions")
            if hasattr(history, 'urls') and callable(getattr(history, 'urls')):
                logger.info(f"Visited {len(history.urls())} URLs")
            if hasattr(history, 'errors') and callable(getattr(history, 'errors')) and history.errors():
                logger.warning(f"Encountered {len(history.errors())} errors during execution")
        except Exception as e:
            logger.debug(f"Could not log execution summary: {e}")

        return history

    except Exception as e:
        logger.error(f"Browser agent execution failed: {str(e)}")
        raise


# Utility functions for creating common configurations

def create_fast_config(**kwargs) -> BrowserHelperConfig:
    """Create a configuration optimized for speed and minimal resource usage (smoke tests)."""
    defaults = {
        "headless": True,
        "use_vision": True,  # ✅ Habilitado para smoke tests
        "enable_memory": False,  # ✅ Deshabilitar memoria para smoke tests (velocidad)
        "max_steps": 30,
        "minimum_wait_page_load_time": 0.3,
        "wait_for_network_idle_page_load_time": 0.5,
        "maximum_wait_page_load_time": 8.0,
        "wait_between_actions": 0.2,
        "model_provider": "gemini",
        "temperature": 0.0,
        "viewport_expansion": 300,
        "deterministic_rendering": True,
        "highlight_elements": False  # Disable for speed
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def create_robust_config(**kwargs) -> BrowserHelperConfig:
    """Create a configuration optimized for reliability and comprehensive testing."""
    defaults = {
        "headless": True,
        "use_vision": True,
        "enable_memory": True,  # ✅ Habilitar memoria de memo para full tests
        "memory_interval": 4,  # Resumir cada 10 pasos para full tests (balance entre eficiencia y memoria)
        "max_steps": 150,
        "minimum_wait_page_load_time": 0.8,
        "wait_for_network_idle_page_load_time": 1.5,
        "maximum_wait_page_load_time": 15.0,
        "wait_between_actions": 0.8,
        "model_provider": "gemini",
        "temperature": 0.0,
        "viewport_expansion": 800,
        "deterministic_rendering": True,
        "highlight_elements": True
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def create_secure_config(allowed_domains: List[str], **kwargs) -> BrowserHelperConfig:
    """Create a configuration with security restrictions for production use."""
    defaults = {
        "headless": True,
        "allowed_domains": allowed_domains,
        "disable_security": False,
        "use_vision": True,
        "max_steps": 100,
        "model_provider": "gemini",
        "temperature": 0.0,
        "viewport_expansion": 500,
        "deterministic_rendering": True,
        "highlight_elements": False  # Disable for production
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)


def validate_config(config: BrowserHelperConfig) -> List[str]:
    """
    Valida la configuración de BrowserHelperConfig y devuelve advertencias.

    Args:
        config: Configuración a validar

    Returns:
        List[str]: Lista de advertencias (vacía si no hay problemas)
    """
    warnings = []

    # Validar configuración de memoria
    if config.enable_memory:
        if not MEMORY_CONFIG_AVAILABLE:
            warnings.append("Memory enabled but MemoryConfig not available. Install browser-use>=0.2.4")

        if config.memory_interval <= 0:
            warnings.append("memory_interval should be greater than 0")

        if config.memory_interval > config.max_steps:
            warnings.append("memory_interval is greater than max_steps - memory won't be used effectively")

        if not config.memory_agent_id:
            warnings.append("memory_agent_id not set - using default agent ID")

    # Validar configuración de pasos
    if config.max_steps <= 0:
        warnings.append("max_steps should be greater than 0")

    # Validar configuración de tiempos
    if config.minimum_wait_page_load_time < 0:
        warnings.append("minimum_wait_page_load_time should be non-negative")

    if config.wait_for_network_idle_page_load_time < config.minimum_wait_page_load_time:
        warnings.append("wait_for_network_idle_page_load_time should be >= minimum_wait_page_load_time")

    if config.maximum_wait_page_load_time < config.wait_for_network_idle_page_load_time:
        warnings.append("maximum_wait_page_load_time should be >= wait_for_network_idle_page_load_time")

    # Validar configuración de visión
    if not config.use_vision and config.enable_memory:
        warnings.append("Memory works better with vision enabled")

    return warnings


def create_debug_config(**kwargs) -> BrowserHelperConfig:
    """Create a configuration optimized for debugging and development."""
    defaults = {
        "headless": False,
        "use_vision": True,
        "enable_memory": True,  # Habilitar memoria para debugging
        "memory_interval": 3,  # Resumir cada 3 pasos para debugging (muy frecuente)
        "max_steps": 50,
        "minimum_wait_page_load_time": 1.0,
        "wait_for_network_idle_page_load_time": 2.0,
        "maximum_wait_page_load_time": 20.0,
        "wait_between_actions": 2.0,  # Slower for observation
        "model_provider": "gemini",
        "temperature": 0.0,
        "viewport_expansion": 1000,
        "deterministic_rendering": True,
        "highlight_elements": True,
        "save_conversation_path": "./debug_conversations"
    }
    defaults.update(kwargs)
    return BrowserHelperConfig(**defaults)





# Export main functions and classes
__all__ = [
    'BrowserHelperConfig',
    'create_and_run_agent',
    'create_fast_config',
    'create_robust_config',
    'create_secure_config',
    'create_debug_config',
    'validate_config',
    'create_llm_instance',
    'create_browser_profile',
    'extract_urls_from_scenario',
    'create_initial_actions_from_url'
]
