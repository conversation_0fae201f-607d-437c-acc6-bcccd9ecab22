"""
<PERSON>ódulo que proporciona una clase para ejecutar tests.
Esta clase es utilizada tanto por la interfaz web (app.py) como por la interfaz de línea de comandos (cli.py).
"""

import os
import sys
import json
import asyncio
import base64
import concurrent.futures
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from browser_use import Browser, Agent as BrowserAgent, Controller
from src.Utilities.browser_helper import (
    create_and_run_agent,
    create_fast_config,
    create_robust_config,
    validate_config
)
from langchain_google_genai import ChatGoogleGenerativeAI
from src.Utilities.utils import controller
import re  # Para expresiones regulares
# PromptManager is deprecated, using PromptService instead
from src.Core.prompt_service import PromptService


class TestExecutor:
    """
    Clase que encapsula la lógica de ejecución de tests.
    """

    def __init__(self, api_key: str, language: Optional[str] = None):
        """
        Inicializa el ejecutor de tests.

        Args:
            api_key: API key de Google Gemini
            language: Idioma de los prompts ('en' o 'es')
        """
        self.api_key = api_key
        self.language = language or os.getenv("PROMPT_LANGUAGE", "en")

        # Configurar el bucle de eventos para Windows si es necesario
        if sys.platform == "win32":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

    def _analyze_test_result(self, history, gherkin_scenario: str) -> Dict[str, Any]:
        """
        Analiza el resultado del test y determina si fue exitoso o falló.

        Args:
            history: Historial de la ejecución del test
            gherkin_scenario: Escenario Gherkin ejecutado

        Returns:
            Dict con información detallada del resultado
        """
        try:
            # Obtener el resultado final del historial
            final_result = history.final_result()

            # Obtener errores del historial
            errors = history.errors() if hasattr(history, 'errors') else []

            # Obtener contenido extraído para análisis adicional
            extracted_content = history.extracted_content() if hasattr(history, 'extracted_content') else []

            # Contar errores no nulos
            error_count = len([error for error in errors if error is not None])

            # Determinar si el test fue exitoso basado en múltiples factores
            is_successful = self._determine_test_success(final_result, errors, extracted_content)

            if is_successful:
                return {
                    "status": "completed",
                    "message": final_result if final_result else "Test completed successfully",
                    "success": True,
                    "error_count": error_count,
                    "details": {
                        "final_result": final_result,
                        "errors_encountered": error_count,
                        "execution_summary": "Test executed successfully with expected results"
                    }
                }
            else:
                # Test falló - proporcionar información detallada del fallo
                failure_reason = self._determine_failure_reason(final_result, errors, extracted_content)

                return {
                    "status": "failed",
                    "message": failure_reason,
                    "success": False,
                    "error_count": error_count,
                    "details": {
                        "final_result": final_result,
                        "errors_encountered": error_count,
                        "failure_reason": failure_reason,
                        "execution_summary": "Test failed to meet expected criteria",
                        "errors": [error for error in errors if error is not None]
                    }
                }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Error analyzing test result: {str(e)}",
                "success": False,
                "error_count": 0,
                "details": {
                    "final_result": None,
                    "errors_encountered": 0,
                    "failure_reason": f"Analysis error: {str(e)}",
                    "execution_summary": "Test result analysis failed"
                }
            }

    def _determine_test_success(self, final_result, errors, extracted_content) -> bool:
        """
        Determina si el test fue exitoso basado en el resultado final, errores y contenido extraído.
        """
        # Si no hay resultado final, considerar como fallo
        if not final_result:
            return False

        # Convertir a string para análisis
        result_text = str(final_result).lower()

        # Palabras clave que indican éxito
        success_keywords = [
            "successful", "success", "completed", "found", "verified",
            "exitoso", "exitosa", "completado", "encontrado", "verificado",
            "passed", "correct", "valid", "working"
        ]

        # Palabras clave que indican fallo
        failure_keywords = [
            "failed", "error", "not found", "unable", "could not", "cannot",
            "falló", "error", "no encontrado", "no se pudo", "no puede",
            "timeout", "exception", "invalid", "incorrect", "broken"
        ]

        # Verificar palabras clave de éxito
        has_success_keywords = any(keyword in result_text for keyword in success_keywords)

        # Verificar palabras clave de fallo
        has_failure_keywords = any(keyword in result_text for keyword in failure_keywords)

        # Si hay palabras de éxito y no hay palabras de fallo, considerar exitoso
        if has_success_keywords and not has_failure_keywords:
            return True

        # Si hay palabras de fallo, considerar como fallo
        if has_failure_keywords:
            return False

        # Si el resultado es muy corto o genérico, verificar errores
        if len(result_text.strip()) < 10:
            # Si hay muchos errores, probablemente falló
            error_count = len([error for error in errors if error is not None])
            return error_count < 3  # Tolerancia de hasta 2 errores

        # Por defecto, si hay un resultado final descriptivo, considerar exitoso
        return len(result_text.strip()) > 20

    def _determine_failure_reason(self, final_result, errors, extracted_content) -> str:
        """
        Determina la razón específica del fallo del test.
        """
        # Si hay un resultado final, usarlo como base
        if final_result:
            result_text = str(final_result)
            if len(result_text) > 10:
                return f"Test failed: {result_text}"

        # Analizar errores para encontrar la causa principal
        non_null_errors = [error for error in errors if error is not None]

        if not non_null_errors:
            return self._handle_no_errors_case(final_result)

        error_types, element_index_errors = self._categorize_errors(non_null_errors)
        most_common_error = max(error_types.items(), key=lambda x: x[1])

        return self._format_error_message(most_common_error, element_index_errors, non_null_errors)

    def _handle_no_errors_case(self, final_result) -> str:
        """Maneja el caso cuando no hay errores específicos."""
        if not final_result:
            return "Test failed: No final result was generated. The test may have been interrupted or the automation agent was unable to complete the scenario."
        return "Test failed for unknown reasons. Please check the execution logs for more details."

    def _categorize_errors(self, non_null_errors) -> Tuple[Dict[str, int], List[str]]:
        """Categoriza los errores por tipo."""
        error_types = {}
        element_index_errors = []

        for error in non_null_errors:
            error_str = str(error).lower()
            error_type = self._identify_error_type(error_str)
            error_types[error_type] = error_types.get(error_type, 0) + 1

            if error_type == "element_index_not_found":
                element_index_errors.append(error_str)

        return error_types, element_index_errors

    def _identify_error_type(self, error_str: str) -> str:
        """Identifica el tipo de error basado en el mensaje."""
        if ("element with index" in error_str or "element index" in error_str) and "does not exist" in error_str:
            return "element_index_not_found"
        elif "timeout" in error_str:
            return "timeout"
        elif "not found" in error_str or "element" in error_str:
            return "element_not_found"
        elif "network" in error_str or "connection" in error_str:
            return "network"
        elif "max_failures" in error_str or "maximum failures" in error_str:
            return "max_failures_reached"
        else:
            return "general"

    def _format_error_message(self, most_common_error: Tuple[str, int], element_index_errors: List[str], non_null_errors: List) -> str:
        """Formatea el mensaje de error basado en el tipo más común."""
        error_type, count = most_common_error

        if error_type == "element_index_not_found":
            return self._format_element_index_error(element_index_errors, count)
        elif error_type == "max_failures_reached":
            return f"Test failed because the browser agent reached the maximum number of consecutive failures ({count} occurrences). This often happens when the agent cannot find the expected elements or perform required actions. Check if the page structure matches expectations or if additional waiting time is needed."
        elif error_type == "timeout":
            return f"Test failed due to timeout errors ({count} occurrences). The page or elements took too long to load."
        elif error_type == "element_not_found":
            return f"Test failed because required elements were not found ({count} occurrences). The page structure may have changed."
        elif error_type == "network":
            return f"Test failed due to network/connection issues ({count} occurrences)."
        else:
            return f"Test failed with {len(non_null_errors)} errors. Last error: {non_null_errors[-1]}"

    def _format_element_index_error(self, element_index_errors: List[str], count: int) -> str:
        """Formatea el mensaje de error específico para errores de índice de elementos."""
        unique_indexes = set()
        for error in element_index_errors:
            match = re.search(r'index (\d+)', error)
            if match:
                unique_indexes.add(match.group(1))

        index_list = ", ".join(sorted(unique_indexes))
        return f"Test failed due to element detection issues. Elements with indexes [{index_list}] were not found on the page ({count} occurrences). This usually indicates that the page structure has changed, elements loaded dynamically after detection, or the page layout is different than expected. Consider checking if the page loaded completely or if authentication is required."

    def save_screenshots_to_files(self, screenshots: List[str], test_id: Optional[str] = None, test_type: str = "test") -> List[str]:
        """
        Guarda las capturas de pantalla como archivos separados y devuelve una lista de rutas.

        Args:
            screenshots: Lista de capturas de pantalla en formato base64
            test_id: Identificador único para la prueba (opcional)
            test_type: Tipo de prueba (test, smoke_test, etc.)

        Returns:
            List[str]: Lista de rutas a los archivos de capturas de pantalla
        """
        if not test_id:
            test_id = datetime.now().strftime("%Y%m%d%H%M%S")

        # Crear directorio para las capturas de pantalla
        tests_dir = os.path.join("tests")
        os.makedirs(tests_dir, exist_ok=True)

        test_dir = os.path.join(tests_dir, f"{test_type}_{test_id}")
        os.makedirs(test_dir, exist_ok=True)

        screenshots_dir = os.path.join(test_dir, "screenshots")
        os.makedirs(screenshots_dir, exist_ok=True)

        screenshot_paths = []
        for i, screenshot in enumerate(screenshots):
            if isinstance(screenshot, str) and screenshot.startswith("data:image"):
                # Extraer datos base64 de la URL de datos
                try:
                    # Formato típico: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
                    image_data = screenshot.split(",")[1]
                    image_bytes = base64.b64decode(image_data)

                    # Guardar imagen
                    screenshot_path = os.path.join(screenshots_dir, f"screenshot_{i+1}.png")
                    with open(screenshot_path, "wb") as f:
                        f.write(image_bytes)

                    # Guardar ruta relativa
                    screenshot_paths.append(os.path.relpath(screenshot_path))
                except Exception as e:
                    print(f"Error al guardar captura de pantalla {i+1}: {str(e)}")
                    continue

        return screenshot_paths

    def save_history_json(self, history: Any, test_id: str, test_type: str = "test", screenshot_paths: Optional[List[str]] = None) -> Optional[str]:
        """
        Guarda el historial de prueba en un archivo JSON y modifica las referencias a capturas de pantalla.

        Args:
            history: Objeto de historial de la prueba
            test_id: Identificador único para la prueba
            test_type: Tipo de prueba (test, smoke_test, etc.)
            screenshot_paths: Lista de rutas a los archivos de capturas de pantalla

        Returns:
            str: Ruta al archivo JSON guardado
        """
        try:
            # Crear directorio principal de tests si no existe
            tests_dir = os.path.join("tests")
            os.makedirs(tests_dir, exist_ok=True)

            # Crear directorio específico para este test
            test_dir = os.path.join(tests_dir, f"{test_type}_{test_id}")
            os.makedirs(test_dir, exist_ok=True)

            # Definir la ruta del archivo JSON
            history_json_path = os.path.join(test_dir, "history.json")

            # Guardar el historial en el archivo JSON
            history.save_to_file(history_json_path)

            # Si hay capturas de pantalla, modificar el JSON para usar referencias a archivos
            if screenshot_paths:
                # Leer el archivo JSON recién guardado
                with open(history_json_path, "r") as f:
                    history_data = json.load(f)

                # Guardar el escenario Gherkin en el historial para referencia futura
                if hasattr(self, 'current_gherkin_scenario'):
                    history_data["gherkin_scenario"] = self.current_gherkin_scenario

                # Contador para las capturas de pantalla
                screenshot_index = 0

                # Recorrer las acciones del modelo y reemplazar las capturas de pantalla
                for action in history_data.get("model_actions", []):
                    if "screenshot" in action and screenshot_index < len(screenshot_paths):
                        # Reemplazar la captura de pantalla con la ruta al archivo
                        action["screenshot_path"] = screenshot_paths[screenshot_index]
                        # Eliminar completamente la captura base64 para reducir el tamaño del JSON
                        action.pop("screenshot", None)
                        screenshot_index += 1

                # Eliminar también las capturas base64 del estado si existen
                if "state" in history_data:
                    if "screenshot" in history_data["state"]:
                        history_data["state"].pop("screenshot", None)

                # Guardar el archivo JSON modificado
                with open(history_json_path, "w") as f:
                    json.dump(history_data, f, indent=2)

            return history_json_path

        except Exception as e:
            print(f"Error al guardar el historial de prueba: {str(e)}")
            return None

    def create_gherkin_scenario(self, instructions: str, url: Optional[str] = None, user_story: Optional[str] = None) -> str:
        """
        Crea un escenario Gherkin a partir de instrucciones, URL y opcionalmente una historia de usuario.

        Args:
        instructions: Instrucciones para la prueba
        url: URL para la prueba (opcional)
        user_story: Historia de usuario (opcional)

        Returns:
        str: Escenario Gherkin generado
        """
        # Procesar la historia de usuario si está disponible
        feature_description = "Smoke Test"
        if user_story:
            # Mejorar la historia de usuario si es posible
            try:
                # Use the new prompt service
                prompt_service = PromptService()
                enhanced_story = prompt_service.enhance_user_story(user_story, self.language)

                feature_description = enhanced_story.split('\n')[0] if '\n' in enhanced_story else enhanced_story
            except Exception as e:
                print(f"Advertencia: No se pudo mejorar la historia de usuario: {str(e)}")
                feature_description = user_story.split('\n')[0] if '\n' in user_story else user_story

        # Si hay una URL, incluirla en el escenario (adaptar al idioma)
        if self.language == "es":
            url_step = f"Given que el usuario navega a {url}" if url else "Given que el usuario sigue las instrucciones"
            then_step = "Then la prueba se completa exitosamente"
            scenario_title = "Ejecutar prueba rápida"
        else:  # Default to English
            url_step = f"Given the user navigates to {url}" if url else "Given the user follows instructions"
            then_step = "Then the test completes successfully"
            scenario_title = "Execute quick test"

        # Crear un escenario más simple y directo para smoke tests
        gherkin_scenario = f"""
Feature: {feature_description}

  Scenario: {scenario_title}
    {url_step}
    When {instructions}
    {then_step}
"""

        # Guardar el escenario para referencia futura
        self.current_gherkin_scenario = gherkin_scenario

        return gherkin_scenario

    async def execute_smoke_test(self, gherkin_scenario: str, url: Optional[str] = None) -> Dict[str, Any]:
        """
        Ejecuta un smoke test basado en un escenario Gherkin.

        Args:
            gherkin_scenario: Escenario Gherkin a ejecutar
            url: URL para la prueba (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        try:
            # Crear configuración optimizada para smoke tests (rápidos y eficientes)
            smoke_config = create_fast_config(
                headless=True,  # ✅ Siempre headless para smoke tests
                use_vision=True,  # ✅ Habilitado para smoke tests
                enable_memory=False,  # ✅ Deshabilitar memoria para smoke tests (velocidad)
                max_steps=30,  # Limitar pasos para smoke tests
                minimum_wait_page_load_time=0.3,
                wait_for_network_idle_page_load_time=0.5,
                maximum_wait_page_load_time=8.0,
                wait_between_actions=0.2,
                viewport_expansion=300,  # Reducir para velocidad
                highlight_elements=False  # Desactivar para velocidad
            )

            # Validar configuración
            warnings = validate_config(smoke_config)
            if warnings:
                print(f"⚠️ Configuración smoke test - warnings: {warnings}")

            # Log de configuración de memoria
            print(f"🧠 Smoke Test - Memoria habilitada: {smoke_config.enable_memory}")
            print("⚡ Smoke Test - Configuración optimizada para velocidad")

            # Usar browser_helper para crear y ejecutar el agente con configuración optimizada
            history = await create_and_run_agent(
                scenario_text=gherkin_scenario,
                controller_instance=controller,
                api_key=self.api_key,
                language=self.language,
                config=smoke_config,
                url=url
            )

            # Generar un ID único para esta prueba
            test_id = datetime.now().strftime("%Y%m%d%H%M%S")

            # Extraer capturas de pantalla
            screenshots = []
            for action in history.model_actions():
                if "screenshot" in action:
                    screenshots.append(action["screenshot"])

            # Guardar capturas de pantalla como archivos separados
            screenshot_paths = []
            if screenshots:
                screenshot_paths = self.save_screenshots_to_files(screenshots, test_id, "smoke_test")

            # Guardar el historial JSON con referencias a las capturas
            history_json_path = self.save_history_json(history, test_id, "smoke_test", screenshot_paths)

            # Analizar el resultado del test usando la nueva función
            analyzed_result = self._analyze_test_result(history, gherkin_scenario)

            # Devolver información sobre la ejecución
            return {
                "success": analyzed_result["success"],
                "test_id": test_id,
                "history": {
                    "urls": history.urls() if hasattr(history, 'urls') else [],
                    "action_names": history.action_names() if hasattr(history, 'action_names') else [],
                    "extracted_content": history.extracted_content() if hasattr(history, 'extracted_content') else [],
                    "errors": history.errors() if hasattr(history, 'errors') else [],
                    "final_result": history.final_result() if hasattr(history, 'final_result') else None,
                    "model_actions": history.model_actions() if hasattr(history, 'model_actions') else []
                },                "history_path": history_json_path,
                "screenshot_paths": screenshot_paths,
                "result": analyzed_result,
                "gherkin_scenario": gherkin_scenario
            }

        except Exception as e:
            print(f"Error durante la ejecución del Smoke Test: {str(e)}")
            # Manejar mejor el error y dar información más específica
            error_msg = str(e)
            if "browser" in error_msg.lower() or "cdp" in error_msg.lower():
                error_msg = f"Error de browser: {error_msg}"
            elif "connection" in error_msg.lower():
                error_msg = f"Error de conexión: {error_msg}"

            return {
                "success": False,
                "error": error_msg,
                "test_id": datetime.now().strftime("%Y%m%d%H%M%S"),
                "history": {
                    "urls": [],
                    "action_names": [],
                    "extracted_content": [],
                    "errors": [error_msg],
                    "final_result": None,
                    "model_actions": []
                },                "history_path": None,
                "screenshot_paths": [],
                "result": {"success": False, "error": error_msg},
                "gherkin_scenario": gherkin_scenario
            }

    def run_smoke_test(self, instructions: str, url: Optional[str] = None, user_story: Optional[str] = None) -> Dict[str, Any]:
        """
        Ejecuta un smoke test con las instrucciones, URL y opcionalmente una historia de usuario.

        Args:
            instructions: Instrucciones para la prueba
            url: URL para la prueba (opcional)
            user_story: Historia de usuario (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        try:
            # Crear el escenario Gherkin
            gherkin_scenario = self.create_gherkin_scenario(instructions, url, user_story)

            # Ejecutar el test con mejor manejo del event loop
            loop = None
            try:
                # Verificar si ya hay un event loop ejecutándose
                try:
                    loop = asyncio.get_running_loop()
                    # Si hay un loop corriendo, crear una tarea
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, self.execute_smoke_test(gherkin_scenario, url))
                        result = future.result()
                except RuntimeError:
                    # No hay loop corriendo, crear uno nuevo
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    result = loop.run_until_complete(self.execute_smoke_test(gherkin_scenario, url))

            finally:
                # Cerrar el loop solo si lo creamos nosotros
                if loop and not loop.is_running():
                    try:
                        loop.close()
                    except Exception:
                        pass  # Ignorar errores al cerrar el loop

            return result

        except Exception as e:
            print(f"Error en run_smoke_test: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "test_id": None,
                "history": None,
                "screenshot_paths": [],
                "history_path": None
            }

    async def execute_full_test(self, gherkin_scenario: str, url: Optional[str] = None) -> Dict[str, Any]:
        """
        Ejecuta un test completo basado en escenarios Gherkin más elaborados.

        Args:
            gherkin_scenario: Escenario Gherkin a ejecutar
            url: URL para la prueba (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        try:
            # Generar un ID único para esta prueba
            test_id = datetime.now().strftime("%Y%m%d%H%M%S")
            # Lista para almacenar todas las rutas de capturas de pantalla
            all_screenshot_paths = []

            # Parse the Gherkin content to extract scenarios
            scenarios = []
            current_scenario = []
            for line in gherkin_scenario.split('\n'):
                if line.strip().startswith('Scenario:'):
                    if current_scenario:
                        scenarios.append('\n'.join(current_scenario))
                    current_scenario = [line]
                elif current_scenario:
                    current_scenario.append(line)
            if current_scenario:
                scenarios.append('\n'.join(current_scenario))

            # Execute each scenario separately
            all_results = []
            all_actions = []
            all_extracted_content = []
            element_xpath_map = {}

            # Crear configuración robusta para tests completos
            full_test_config = create_robust_config(
                headless=True,  # Headless para ejecución automatizada
                use_vision=True,  # Visión completa para tests detallados
                enable_memory=True,  # ✅ Habilitar memoria de memo para tests complejos
                memory_agent_id=f"full_test_{test_id}",  # ID único para este test
                memory_interval=10,  # Resumir cada 10 pasos para full tests (balance entre eficiencia y memoria)
                max_steps=150,  # Más pasos para tests complejos
                minimum_wait_page_load_time=0.8,
                wait_for_network_idle_page_load_time=1.5,
                maximum_wait_page_load_time=15.0,
                wait_between_actions=0.8,
                viewport_expansion=800,  # Mayor contexto visual
                deterministic_rendering=True,  # Renderizado consistente
                highlight_elements=True  # Mantener highlighting para debugging
            )

            # Validar configuración
            warnings = validate_config(full_test_config)
            if warnings:
                print(f"⚠️ Configuración full test - warnings: {warnings}")

            # Log de configuración de memoria
            print(f"🧠 Full Test - Memoria habilitada: {full_test_config.enable_memory}")
            if full_test_config.enable_memory:
                print(f"🧠 Full Test - Agent ID: {full_test_config.memory_agent_id}")
                print(f"🧠 Full Test - Memory interval: {full_test_config.memory_interval} pasos")

            for scenario in scenarios:
                # Usar browser_helper para crear y ejecutar el agente con configuración robusta
                history = await create_and_run_agent(
                    scenario_text=scenario,
                    controller_instance=controller,
                    api_key=self.api_key,
                    language=self.language,
                    config=full_test_config,
                    url=url  # ✅ Pasar URL directamente como parámetro
                )

                # Generar un ID único para este escenario
                scenario_test_id = f"{test_id}_scenario_{len(all_results) + 1}"

                # Extraer capturas de pantalla
                scenario_screenshots = []
                for action in history.model_actions():
                    if "screenshot" in action:
                        scenario_screenshots.append(action["screenshot"])

                # Guardar capturas de pantalla como archivos separados
                scenario_screenshot_paths = []
                if scenario_screenshots:
                    scenario_screenshot_paths = self.save_screenshots_to_files(scenario_screenshots, scenario_test_id, "test")
                    # Añadir las rutas a la lista general
                    all_screenshot_paths.extend(scenario_screenshot_paths)

                # Guardar el historial JSON con referencias a las capturas
                history_json_path = self.save_history_json(history, scenario_test_id, "test", scenario_screenshot_paths)

                # Analizar el resultado del escenario usando la nueva función
                analyzed_result = self._analyze_test_result(history, scenario)

                # Añadir información de capturas de pantalla al resultado
                analyzed_result["screenshot_paths"] = scenario_screenshot_paths
                analyzed_result["history_json_path"] = history_json_path

                all_results.append(analyzed_result)

                # Process model actions to extract element details
                for i, action_data in enumerate(history.model_actions()):
                    action_name = history.action_names()[i] if i < len(history.action_names()) else "Unknown Action"

                    # Create a detail record for each action
                    action_detail = {
                        "name": action_name,
                        "index": i,
                        "element_details": {}
                    }

                    # Check if this is a get_xpath_of_element action
                    if "get_xpath_of_element" in action_data:
                        element_index = action_data["get_xpath_of_element"].get("index")
                        action_detail["element_details"]["index"] = element_index

                        # Check if the interacted_element field contains XPath information
                        if "interacted_element" in action_data and action_data["interacted_element"]:
                            element_info = action_data["interacted_element"]

                            # Extract XPath from the DOMHistoryElement string
                            import re
                            xpath_match = re.search(r"xpath='([^']+)'", str(element_info))
                            if xpath_match:
                                xpath = xpath_match.group(1)
                                element_xpath_map[element_index] = xpath
                                action_detail["element_details"]["xpath"] = xpath

                    # Check if this is an action on an element
                    elif any(key in action_data for key in ["input_text", "click_element", "perform_element_action"]):
                        # Find the action parameters
                        for key in ["input_text", "click_element", "perform_element_action"]:
                            if key in action_data:
                                action_params = action_data[key]
                                if "index" in action_params:
                                    element_index = action_params["index"]
                                    action_detail["element_details"]["index"] = element_index

                                    # If we have already captured the XPath for this element, add it
                                    if element_index in element_xpath_map:
                                        action_detail["element_details"]["xpath"] = element_xpath_map[element_index]

                                    # Also check interacted_element
                                    if "interacted_element" in action_data and action_data["interacted_element"]:
                                        element_info = action_data["interacted_element"]
                                        import re
                                        xpath_match = re.search(r"xpath='([^']+)'", str(element_info))
                                        if xpath_match:
                                            xpath = xpath_match.group(1)
                                            element_xpath_map[element_index] = xpath
                                            action_detail["element_details"]["xpath"] = xpath

                    all_actions.append(action_detail)

                # Also extract from content if available
                for content in history.extracted_content():
                    all_extracted_content.append(content)

                    # Look for XPath information in extracted content
                    if isinstance(content, str):
                        import re
                        xpath_match = re.search(r"The xpath of the element is (.+)", content)
                        if xpath_match:
                            xpath = xpath_match.group(1)
                            # Try to match with an element index from previous actions
                            index_match = re.search(r"element (\d+)", content)
                            if index_match:
                                element_index = int(index_match.group(1))
                                element_xpath_map[element_index] = xpath

            # Determinar el éxito general basado en todos los resultados
            overall_success = all(result.get("success", False) for result in all_results)

            # Crear un resumen de los resultados
            failed_scenarios = [i for i, result in enumerate(all_results) if not result.get("success", False)]

            # Devolver información sobre la ejecución
            return {
                "success": overall_success,
                "test_id": test_id,
                "history": history,  # Último objeto history para compatibilidad
                "all_results": all_results,
                "screenshot_paths": all_screenshot_paths,
                "detailed_actions": all_actions,
                "element_xpaths": element_xpath_map,
                "extracted_content": all_extracted_content,
                "gherkin_scenario": gherkin_scenario,
                "summary": {
                    "total_scenarios": len(all_results),
                    "successful_scenarios": len(all_results) - len(failed_scenarios),
                    "failed_scenarios": len(failed_scenarios),
                    "failed_scenario_indices": failed_scenarios,
                    "overall_success": overall_success
                }
            }

        except Exception as e:
            print(f"Error durante la ejecución del Full Test: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def run_full_test(self, gherkin_scenario: str, url: Optional[str] = None) -> Dict[str, Any]:
        """
        Ejecuta un test completo basado en escenarios Gherkin.

        Args:
            gherkin_scenario: Escenario Gherkin a ejecutar
            url: URL para la prueba (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        # Ejecutar el test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(self.execute_full_test(gherkin_scenario, url))
        loop.close()

        return result
