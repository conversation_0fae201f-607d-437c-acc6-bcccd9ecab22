"""
Checklist Manager for Agents QA
Manages the state of tasks in the test generation workflow
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

class ChecklistItem:
    """Represents a single item in the checklist"""
    def __init__(
        self, 
        id: str, 
        title: str, 
        description: str, 
        completed: bool = False,
        data: Optional[Dict[str, Any]] = None,
        dependent_on: Optional[List[str]] = None
    ):
        self.id = id
        self.title = title
        self.description = description
        self.completed = completed
        self.data = data or {}
        self.dependent_on = dependent_on or []
        self.completion_time = None
    
    def complete(self, data: Optional[Dict[str, Any]] = None) -> None:
        """Mark the item as completed and store associated data"""
        self.completed = True
        self.completion_time = datetime.now().isoformat()
        if data:
            self.data.update(data)
    
    def reset(self) -> None:
        """Reset the item to incomplete state"""
        self.completed = False
        self.completion_time = None
    
    def is_available(self, completed_items: List[str]) -> bool:
        """Check if this item is available based on dependencies"""
        if not self.dependent_on:
            return True
        return all(dep in completed_items for dep in self.dependent_on)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "completed": self.completed,
            "data": self.data,
            "dependent_on": self.dependent_on,
            "completion_time": self.completion_time
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChecklistItem':
        """Create a ChecklistItem from a dictionary"""
        item = cls(
            id=data["id"],
            title=data["title"],
            description=data["description"],
            completed=data["completed"],
            data=data.get("data", {}),
            dependent_on=data.get("dependent_on", [])
        )
        item.completion_time = data.get("completion_time")
        return item


class ChecklistManager:
    """Manages a checklist of tasks for the test generation workflow"""
    
    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id or datetime.now().strftime("%Y%m%d%H%M%S")
        self.items: Dict[str, ChecklistItem] = {}
        self.initialize_default_checklist()
    
    def initialize_default_checklist(self) -> None:
        """Initialize the default checklist items for the test generation workflow"""
        default_items = [
            ChecklistItem(
                id="user_story",
                title="Definir Historia de Usuario",
                description="Proporcionar una historia de usuario básica para iniciar el proceso de generación de pruebas."
            ),
            ChecklistItem(
                id="enhanced_story",
                title="Mejorar Historia de Usuario",
                description="Mejorar la historia de usuario con detalles adicionales, criterios de aceptación y contexto.",
                dependent_on=["user_story"]
            ),
            ChecklistItem(
                id="manual_tests",
                title="Generar Casos de Prueba Manuales",
                description="Crear casos de prueba manuales detallados basados en la historia de usuario mejorada.",
                dependent_on=["enhanced_story"]
            ),
            ChecklistItem(
                id="gherkin",
                title="Crear Escenarios Gherkin",
                description="Transformar los casos de prueba manuales en escenarios Gherkin estructurados.",
                dependent_on=["manual_tests"]
            ),
            ChecklistItem(
                id="browser_execution",
                title="Ejecutar Pruebas en Navegador",
                description="Ejecutar los escenarios Gherkin en un navegador real para capturar interacciones.",
                dependent_on=["gherkin"]
            ),
            ChecklistItem(
                id="code_generation",
                title="Generar Código de Automatización",
                description="Generar código de automatización basado en los escenarios Gherkin y las interacciones del navegador.",
                dependent_on=["browser_execution"]
            ),
            ChecklistItem(
                id="save_results",
                title="Guardar Resultados",
                description="Guardar todos los artefactos generados para uso futuro.",
                dependent_on=["code_generation"]
            )
        ]
        
        for item in default_items:
            self.items[item.id] = item
    
    def get_item(self, item_id: str) -> Optional[ChecklistItem]:
        """Get a checklist item by ID"""
        return self.items.get(item_id)
    
    def complete_item(self, item_id: str, data: Optional[Dict[str, Any]] = None) -> bool:
        """Mark a checklist item as completed"""
        item = self.get_item(item_id)
        if item:
            item.complete(data)
            return True
        return False
    
    def reset_item(self, item_id: str) -> bool:
        """Reset a checklist item to incomplete"""
        item = self.get_item(item_id)
        if item:
            item.reset()
            return True
        return False
    
    def get_available_items(self) -> List[ChecklistItem]:
        """Get all items that are available to be worked on"""
        completed_ids = [id for id, item in self.items.items() if item.completed]
        return [
            item for item in self.items.values() 
            if not item.completed and item.is_available(completed_ids)
        ]
    
    def get_all_items(self) -> List[ChecklistItem]:
        """Get all checklist items"""
        return list(self.items.values())
    
    def get_progress(self) -> float:
        """Get the overall progress as a percentage"""
        if not self.items:
            return 0.0
        completed = sum(1 for item in self.items.values() if item.completed)
        return (completed / len(self.items)) * 100
    
    def save_to_file(self, filepath: Optional[str] = None) -> str:
        """Save the checklist state to a file"""
        if not filepath:
            os.makedirs("saved_sessions", exist_ok=True)
            filepath = f"saved_sessions/checklist_{self.session_id}.json"
        
        data = {
            "session_id": self.session_id,
            "items": {id: item.to_dict() for id, item in self.items.items()},
            "timestamp": datetime.now().isoformat()
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)
        
        return filepath
    
    @classmethod
    def load_from_file(cls, filepath: str) -> 'ChecklistManager':
        """Load a checklist from a file"""
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        manager = cls(session_id=data.get("session_id"))
        manager.items = {}
        
        for id, item_data in data.get("items", {}).items():
            manager.items[id] = ChecklistItem.from_dict(item_data)
        
        return manager
