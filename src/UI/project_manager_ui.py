import streamlit as st
import pandas as pd
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

from src.Utilities.project_manager import Project, TestSuite, TestCase
from src.Utilities.project_manager_service import ProjectManagerService


class ProjectManagerUI:
    """
    Interfaz de usuario para gestionar proyectos, suites de pruebas y casos de prueba.
    """
    def __init__(self, project_manager: ProjectManagerService):
        self.project_manager = project_manager

        # Inicializar estados de sesión si no existen
        if "selected_project_id" not in st.session_state:
            st.session_state.selected_project_id = None
        if "selected_suite_id" not in st.session_state:
            st.session_state.selected_suite_id = None
        if "selected_test_id" not in st.session_state:
            st.session_state.selected_test_id = None
        if "view_mode" not in st.session_state:
            st.session_state.view_mode = "projects"  # projects, suites, tests, test_details
        if "edit_mode" not in st.session_state:
            st.session_state.edit_mode = False
        if "create_mode" not in st.session_state:
            st.session_state.create_mode = False

    def render(self):
        """Renderiza la interfaz de usuario del gestor de proyectos."""
        st.markdown('<h2 class="glow-text">Gestor de Proyectos</h2>', unsafe_allow_html=True)

        # Barra de navegación
        self._render_navigation()

        # Contenido principal
        if st.session_state.view_mode == "projects":
            self._render_projects_view()
        elif st.session_state.view_mode == "suites":
            self._render_suites_view()
        elif st.session_state.view_mode == "tests":
            self._render_tests_view()
        elif st.session_state.view_mode == "test_details":
            self._render_test_details_view()

    def _render_navigation(self):
        """Renderiza la barra de navegación."""
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("Proyectos", key="nav_projects"):
                st.session_state.view_mode = "projects"
                st.session_state.selected_suite_id = None
                st.session_state.selected_test_id = None
                st.session_state.edit_mode = False
                st.session_state.create_mode = False
                st.rerun()

        with col2:
            if st.session_state.selected_project_id:
                if st.button("Suites de Prueba", key="nav_suites"):
                    st.session_state.view_mode = "suites"
                    st.session_state.selected_test_id = None
                    st.session_state.edit_mode = False
                    st.session_state.create_mode = False
                    st.rerun()

        with col3:
            if st.session_state.selected_project_id and st.session_state.selected_suite_id:
                if st.button("Casos de Prueba", key="nav_tests"):
                    st.session_state.view_mode = "tests"
                    st.session_state.edit_mode = False
                    st.session_state.create_mode = False
                    st.rerun()

        with col4:
            if st.session_state.selected_project_id and st.session_state.selected_suite_id and st.session_state.selected_test_id:
                if st.button("Detalles del Caso", key="nav_test_details"):
                    st.session_state.view_mode = "test_details"
                    st.session_state.edit_mode = False
                    st.session_state.create_mode = False
                    st.rerun()

    def _render_projects_view(self):
        """Renderiza la vista de proyectos."""
        st.markdown('<h3 class="glow-text">Proyectos</h3>', unsafe_allow_html=True)

        # Botón para crear un nuevo proyecto
        if not st.session_state.create_mode:
            if st.button("Crear Nuevo Proyecto", key="create_project_btn"):
                st.session_state.create_mode = True
                st.rerun()

        # Formulario para crear un nuevo proyecto
        if st.session_state.create_mode:
            with st.form("create_project_form"):
                st.markdown('<h4>Crear Nuevo Proyecto</h4>', unsafe_allow_html=True)
                project_name = st.text_input("Nombre del Proyecto", key="new_project_name")
                project_description = st.text_area("Descripción", key="new_project_description")
                project_tags = st.text_input("Etiquetas (separadas por comas)", key="new_project_tags")

                col1, col2 = st.columns(2)
                with col1:
                    submit_button = st.form_submit_button("Guardar")
                with col2:
                    if st.form_submit_button("Cancelar"):
                        st.session_state.create_mode = False
                        st.rerun()

                if submit_button and project_name:
                    tags = [tag.strip() for tag in project_tags.split(",")] if project_tags else []
                    self.project_manager.create_project(
                        name=project_name,
                        description=project_description,
                        tags=tags
                    )
                    st.session_state.create_mode = False
                    st.success(f"Proyecto '{project_name}' creado exitosamente.")
                    st.rerun()

        # Mostrar lista de proyectos
        projects = self.project_manager.get_all_projects()
        if not projects:
            st.info("No hay proyectos creados. Crea un nuevo proyecto para comenzar.")
            return

        # Crear una tabla de proyectos
        project_data = []
        for project in projects:
            project_data.append({
                "ID": project.project_id,
                "Nombre": project.name,
                "Descripción": project.description,
                "Suites": len(project.test_suites),
                "Creado": datetime.fromisoformat(project.created_at).strftime("%Y-%m-%d %H:%M"),
                "Actualizado": datetime.fromisoformat(project.updated_at).strftime("%Y-%m-%d %H:%M")
            })

        if project_data:
            df = pd.DataFrame(project_data)
            st.dataframe(df, use_container_width=True)

            # Selección de proyecto
            selected_project_id = st.selectbox(
                "Seleccionar Proyecto",
                options=[p["ID"] for p in project_data],
                format_func=lambda x: next((p["Nombre"] for p in project_data if p["ID"] == x), x),
                key="project_selector"
            )

            if selected_project_id:
                st.session_state.selected_project_id = selected_project_id

                col1, col2, col3 = st.columns(3)
                with col1:
                    if st.button("Ver Suites de Prueba", key="view_suites_btn"):
                        st.session_state.view_mode = "suites"
                        st.rerun()
                with col2:
                    if st.button("Editar Proyecto", key="edit_project_btn"):
                        st.session_state.edit_mode = True
                        st.rerun()
                with col3:
                    if st.button("Eliminar Proyecto", key="delete_project_btn"):
                        if self.project_manager.delete_project(selected_project_id):
                            st.session_state.selected_project_id = None
                            st.success("Proyecto eliminado exitosamente.")
                            st.rerun()
                        else:
                            st.error("No se pudo eliminar el proyecto.")

        # Formulario para editar un proyecto existente
        if st.session_state.edit_mode and st.session_state.selected_project_id:
            project = self.project_manager.get_project(st.session_state.selected_project_id)
            if project:
                with st.form("edit_project_form"):
                    st.markdown(f'<h4>Editar Proyecto: {project.name}</h4>', unsafe_allow_html=True)
                    project_name = st.text_input("Nombre del Proyecto", value=project.name, key="edit_project_name")
                    project_description = st.text_area("Descripción", value=project.description, key="edit_project_description")
                    project_tags = st.text_input("Etiquetas (separadas por comas)", value=",".join(project.tags), key="edit_project_tags")

                    col1, col2 = st.columns(2)
                    with col1:
                        submit_button = st.form_submit_button("Guardar Cambios")
                    with col2:
                        if st.form_submit_button("Cancelar"):
                            st.session_state.edit_mode = False
                            st.rerun()

                    if submit_button and project_name:
                        tags = [tag.strip() for tag in project_tags.split(",")] if project_tags else []
                        self.project_manager.update_project(
                            project_id=project.project_id,
                            name=project_name,
                            description=project_description,
                            tags=tags
                        )
                        st.session_state.edit_mode = False
                        st.success(f"Proyecto '{project_name}' actualizado exitosamente.")
                        st.rerun()

    def _render_suites_view(self):
        """Renderiza la vista de suites de pruebas."""
        if not st.session_state.selected_project_id:
            st.warning("No hay un proyecto seleccionado.")
            return

        project = self.project_manager.get_project(st.session_state.selected_project_id)
        if not project:
            st.error("El proyecto seleccionado no existe.")
            return

        st.markdown(f'<h3 class="glow-text">Suites de Prueba - Proyecto: {project.name}</h3>', unsafe_allow_html=True)

        # Botón para crear una nueva suite
        if not st.session_state.create_mode:
            if st.button("Crear Nueva Suite de Pruebas", key="create_suite_btn"):
                st.session_state.create_mode = True
                st.rerun()

        # Formulario para crear una nueva suite
        if st.session_state.create_mode:
            with st.form("create_suite_form"):
                st.markdown('<h4>Crear Nueva Suite de Pruebas</h4>', unsafe_allow_html=True)
                suite_name = st.text_input("Nombre de la Suite", key="new_suite_name")
                suite_description = st.text_area("Descripción", key="new_suite_description")
                suite_tags = st.text_input("Etiquetas (separadas por comas)", key="new_suite_tags")

                col1, col2 = st.columns(2)
                with col1:
                    submit_button = st.form_submit_button("Guardar")
                with col2:
                    if st.form_submit_button("Cancelar"):
                        st.session_state.create_mode = False
                        st.rerun()

                if submit_button and suite_name:
                    tags = [tag.strip() for tag in suite_tags.split(",")] if suite_tags else []
                    self.project_manager.create_test_suite(
                        project_id=project.project_id,
                        name=suite_name,
                        description=suite_description,
                        tags=tags
                    )
                    st.session_state.create_mode = False
                    st.success(f"Suite de pruebas '{suite_name}' creada exitosamente.")
                    st.rerun()

        # Mostrar lista de suites
        suites = project.get_all_test_suites()
        if not suites:
            st.info("No hay suites de pruebas creadas en este proyecto. Crea una nueva suite para comenzar.")
            return

        # Crear una tabla de suites
        suite_data = []
        for suite in suites:
            suite_data.append({
                "ID": suite.suite_id,
                "Nombre": suite.name,
                "Descripción": suite.description,
                "Casos de Prueba": len(suite.test_cases),
                "Creado": datetime.fromisoformat(suite.created_at).strftime("%Y-%m-%d %H:%M"),
                "Actualizado": datetime.fromisoformat(suite.updated_at).strftime("%Y-%m-%d %H:%M")
            })

        if suite_data:
            df = pd.DataFrame(suite_data)
            st.dataframe(df, use_container_width=True)

            # Selección de suite
            selected_suite_id = st.selectbox(
                "Seleccionar Suite de Pruebas",
                options=[s["ID"] for s in suite_data],
                format_func=lambda x: next((s["Nombre"] for s in suite_data if s["ID"] == x), x),
                key="suite_selector"
            )

            if selected_suite_id:
                st.session_state.selected_suite_id = selected_suite_id

                col1, col2, col3 = st.columns(3)
                with col1:
                    if st.button("Ver Casos de Prueba", key="view_tests_btn"):
                        st.session_state.view_mode = "tests"
                        st.rerun()
                with col2:
                    if st.button("Editar Suite", key="edit_suite_btn"):
                        st.session_state.edit_mode = True
                        st.rerun()
                with col3:
                    if st.button("Eliminar Suite", key="delete_suite_btn"):
                        if self.project_manager.delete_test_suite(project.project_id, selected_suite_id):
                            st.session_state.selected_suite_id = None
                            st.success("Suite de pruebas eliminada exitosamente.")
                            st.rerun()
                        else:
                            st.error("No se pudo eliminar la suite de pruebas.")

        # Formulario para editar una suite existente
        if st.session_state.edit_mode and st.session_state.selected_suite_id:
            suite = self.project_manager.get_test_suite(project.project_id, st.session_state.selected_suite_id)
            if suite:
                with st.form("edit_suite_form"):
                    st.markdown(f'<h4>Editar Suite de Pruebas: {suite.name}</h4>', unsafe_allow_html=True)
                    suite_name = st.text_input("Nombre de la Suite", value=suite.name, key="edit_suite_name")
                    suite_description = st.text_area("Descripción", value=suite.description, key="edit_suite_description")
                    suite_tags = st.text_input("Etiquetas (separadas por comas)", value=",".join(suite.tags), key="edit_suite_tags")

                    col1, col2 = st.columns(2)
                    with col1:
                        submit_button = st.form_submit_button("Guardar Cambios")
                    with col2:
                        if st.form_submit_button("Cancelar"):
                            st.session_state.edit_mode = False
                            st.rerun()

                    if submit_button and suite_name:
                        tags = [tag.strip() for tag in suite_tags.split(",")] if suite_tags else []
                        self.project_manager.update_test_suite(
                            project_id=project.project_id,
                            suite_id=suite.suite_id,
                            name=suite_name,
                            description=suite_description,
                            tags=tags
                        )
                        st.session_state.edit_mode = False
                        st.success(f"Suite de pruebas '{suite_name}' actualizada exitosamente.")
                        st.rerun()

    def _render_tests_view(self):
        """Renderiza la vista de casos de prueba."""
        if not st.session_state.selected_project_id or not st.session_state.selected_suite_id:
            st.warning("No hay un proyecto o suite seleccionada.")
            return

        project = self.project_manager.get_project(st.session_state.selected_project_id)
        if not project:
            st.error("El proyecto seleccionado no existe.")
            return

        suite = self.project_manager.get_test_suite(project.project_id, st.session_state.selected_suite_id)
        if not suite:
            st.error("La suite de pruebas seleccionada no existe.")
            return

        st.markdown(f'<h3 class="glow-text">Casos de Prueba - Suite: {suite.name}</h3>', unsafe_allow_html=True)

        # Botón para crear un nuevo caso de prueba
        if not st.session_state.create_mode:
            if st.button("Crear Nuevo Caso de Prueba", key="create_test_btn"):
                st.session_state.create_mode = True
                st.rerun()

        # Formulario para crear un nuevo caso de prueba
        if st.session_state.create_mode:
            with st.form("create_test_form"):
                st.markdown('<h4>Crear Nuevo Caso de Prueba</h4>', unsafe_allow_html=True)
                test_name = st.text_input("Nombre del Caso de Prueba", key="new_test_name")
                test_description = st.text_area("Descripción", key="new_test_description")
                test_instrucciones = st.text_area("Instrucciones", key="new_test_instrucciones",
                                                help="Instrucciones específicas para ejecutar la prueba")
                test_historia_de_usuario = st.text_area("Historia de Usuario", key="new_test_historia_de_usuario",
                                                       help="Historia de usuario que describe el comportamiento esperado")
                test_url = st.text_input("URL", key="new_test_url",
                                        help="URL base para ejecutar la prueba")
                test_gherkin = st.text_area("Gherkin", key="new_test_gherkin")
                test_tags = st.text_input("Etiquetas (separadas por comas)", key="new_test_tags")

                col1, col2 = st.columns(2)
                with col1:
                    submit_button = st.form_submit_button("Guardar")
                with col2:
                    if st.form_submit_button("Cancelar"):
                        st.session_state.create_mode = False
                        st.rerun()

                if submit_button and test_name:
                    tags = [tag.strip() for tag in test_tags.split(",")] if test_tags else []
                    self.project_manager.create_test_case(
                        project_id=project.project_id,
                        suite_id=suite.suite_id,
                        name=test_name,
                        description=test_description,
                        instrucciones=test_instrucciones,
                        historia_de_usuario=test_historia_de_usuario,
                        url=test_url,
                        gherkin=test_gherkin,
                        tags=tags
                    )
                    st.session_state.create_mode = False
                    st.success(f"Caso de prueba '{test_name}' creado exitosamente.")
                    st.rerun()


        # Mostrar lista de casos de prueba
        test_cases = suite.get_all_test_cases()
        if not test_cases:
            st.info("No hay casos de prueba en esta suite. Crea un nuevo caso de prueba para comenzar.")
            return

        # Crear una tabla de casos de prueba
        test_data = []
        for test in test_cases:
            test_data.append({
                "ID": test.test_id,
                "Nombre": test.name,
                "Descripción": test.description,
                "Estado": test.status,
                "Última Ejecución": test.last_execution,
                "Creado": datetime.fromisoformat(test.created_at).strftime("%Y-%m-%d %H:%M")
            })

        if test_data:
            df_tests = pd.DataFrame(test_data)
            st.dataframe(df_tests, use_container_width=True, hide_index=True)

            # Selección de caso de prueba
            selected_test_id_options = [tc["ID"] for tc in test_data]
            selected_test_id_display = {tc["ID"]: tc["Nombre"] for tc in test_data}

            if not selected_test_id_options:
                st.session_state.selected_test_id = None # Asegurar que no haya selección si no hay opciones
            elif st.session_state.selected_test_id not in selected_test_id_options:
                 st.session_state.selected_test_id = selected_test_id_options[0] # Seleccionar el primero por defecto si la selección actual no es válida


            st.session_state.selected_test_id = st.selectbox(
                "Seleccionar Caso de Prueba para Acciones",
                options=selected_test_id_options,
                format_func=lambda x: selected_test_id_display.get(x, x),
                index=selected_test_id_options.index(st.session_state.selected_test_id) if st.session_state.selected_test_id and st.session_state.selected_test_id in selected_test_id_options else 0,
                key="test_selector_actions"
            )


            # Botones de acción para la suite y casos de prueba
            st.markdown("---")
            col_run_suite, col_run_selected, col_details, col_edit, col_delete = st.columns([2,2,1,1,1])

            with col_run_suite:
                if st.button("Ejecutar Suite Completa", key="run_full_suite_btn", use_container_width=True):
                    # Lógica para ejecutar la suite completa
                    # Esto se conectará con el servicio en app.py
                    st.session_state.action = "run_suite"
                    st.session_state.action_params = {
                        "project_id": project.project_id,
                        "suite_id": suite.suite_id
                    }
                    st.success(f"Ejecutando la suite completa: {suite.name}")
                    # En una aplicación real, aquí se llamaría a la función de ejecución
                    # y se actualizaría el estado de los casos de prueba.
                    # Por ahora, solo mostramos un mensaje.
                    # st.rerun() # Puede ser necesario para actualizar la UI después de la ejecución

            if st.session_state.selected_test_id:
                with col_run_selected:
                    if st.button("Ejecutar Caso Seleccionado", key="run_selected_test_btn", use_container_width=True):
                        # Usar la misma lógica que el botón "Ejecutar Prueba" de la vista de detalles
                        st.session_state.run_test = {
                            "project_id": project.project_id,
                            "suite_id": suite.suite_id,
                            "test_id": st.session_state.selected_test_id
                        }
                        st.rerun()

                with col_details:
                    if st.button("Ver Detalles", key="view_test_details_btn", use_container_width=True):
                        st.session_state.view_mode = "test_details"
                        st.rerun()
                with col_edit:
                    if st.button("Editar", key="edit_test_btn", use_container_width=True):
                        st.session_state.edit_mode = True
                        # La lógica de edición se manejará en el formulario de edición
                        st.rerun()
                with col_delete:
                    if st.button("Eliminar", key="delete_test_btn", use_container_width=True):
                        if self.project_manager.delete_test_case(project.project_id, suite.suite_id, st.session_state.selected_test_id):
                            st.session_state.selected_test_id = None
                            st.success("Caso de prueba eliminado exitosamente.")
                            st.rerun()
                        else:
                            st.error("No se pudo eliminar el caso de prueba.")
            else:
                st.info("Selecciona un caso de prueba para ver más acciones.")


        # Formulario para editar un caso de prueba existente
        if st.session_state.edit_mode and st.session_state.selected_test_id:
            test_case = self.project_manager.get_test_case(project.project_id, suite.suite_id, st.session_state.selected_test_id)
            if test_case:
                with st.form("edit_test_form"):
                    st.markdown(f'<h4>Editar Caso de Prueba: {test_case.name}</h4>', unsafe_allow_html=True)
                    test_name = st.text_input("Nombre del Caso de Prueba", value=test_case.name, key="edit_test_name")
                    test_description = st.text_area("Descripción", value=test_case.description, key="edit_test_description")
                    test_instrucciones = st.text_area("Instrucciones", value=test_case.instrucciones, key="edit_test_instrucciones",
                                                    help="Instrucciones específicas para ejecutar la prueba")
                    test_historia_de_usuario = st.text_area("Historia de Usuario", value=test_case.historia_de_usuario, key="edit_test_historia_de_usuario",
                                                           help="Historia de usuario que describe el comportamiento esperado")
                    test_url = st.text_input("URL", value=test_case.url, key="edit_test_url",
                                            help="URL base para ejecutar la prueba")
                    test_gherkin = st.text_area("Gherkin", value=test_case.gherkin, key="edit_test_gherkin")
                    test_tags = st.text_input("Etiquetas (separadas por comas)", value=",".join(test_case.tags), key="edit_test_tags")

                    col1, col2 = st.columns(2)
                    with col1:
                        submit_button = st.form_submit_button("Guardar Cambios")
                    with col2:
                        if st.form_submit_button("Cancelar"):
                            st.session_state.edit_mode = False
                            st.rerun()

                    if submit_button and test_name:
                        tags = [tag.strip() for tag in test_tags.split(",")] if test_tags else []
                        self.project_manager.update_test_case(
                            project_id=project.project_id,
                            suite_id=suite.suite_id,
                            test_id=test_case.test_id,
                            name=test_name,
                            description=test_description,
                            instrucciones=test_instrucciones,
                            historia_de_usuario=test_historia_de_usuario,
                            url=test_url,
                            gherkin=test_gherkin,
                            tags=tags
                        )
                        st.session_state.edit_mode = False
                        st.success(f"Caso de prueba '{test_name}' actualizado exitosamente.")
                        st.rerun()

    def _render_test_details_view(self):
        """Renderiza la vista de detalles del caso de prueba."""
        if not st.session_state.selected_project_id or not st.session_state.selected_suite_id or not st.session_state.selected_test_id:
            st.warning("No hay un caso de prueba seleccionado.")
            return

        project = self.project_manager.get_project(st.session_state.selected_project_id)
        if not project:
            st.error("El proyecto seleccionado no existe.")
            return

        suite = self.project_manager.get_test_suite(project.project_id, st.session_state.selected_suite_id)
        if not suite:
            st.error("La suite de pruebas seleccionada no existe.")
            return

        test_case = self.project_manager.get_test_case(project.project_id, suite.suite_id, st.session_state.selected_test_id)
        if not test_case:
            st.error("El caso de prueba seleccionado no existe.")
            return

        # Mostrar detalles del caso de prueba
        st.markdown(f'<h3 class="glow-text">Detalles del Caso de Prueba: {test_case.name}</h3>', unsafe_allow_html=True)

        # Información general
        st.markdown('<h4>Información General</h4>', unsafe_allow_html=True)
        col1, col2 = st.columns(2)
        with col1:
            st.markdown(f"**ID:** {test_case.test_id}")
            st.markdown(f"**Nombre:** {test_case.name}")
            st.markdown(f"**Estado:** {test_case.status}")
            st.markdown(f"**Etiquetas:** {', '.join(test_case.tags) if test_case.tags else 'Ninguna'}")
        with col2:
            st.markdown(f"**Creado:** {datetime.fromisoformat(test_case.created_at).strftime('%Y-%m-%d %H:%M')}")
            st.markdown(f"**Actualizado:** {datetime.fromisoformat(test_case.updated_at).strftime('%Y-%m-%d %H:%M')}")
            st.markdown(f"**Última Ejecución:** {datetime.fromisoformat(test_case.last_execution).strftime('%Y-%m-%d %H:%M') if test_case.last_execution else 'No ejecutado'}")

        st.markdown('<hr>', unsafe_allow_html=True)

        # Descripción
        st.markdown('<h4>Descripción</h4>', unsafe_allow_html=True)
        st.markdown(test_case.description if test_case.description else "Sin descripción")

        st.markdown('<hr>', unsafe_allow_html=True)

        # URL
        st.markdown('<h4>URL</h4>', unsafe_allow_html=True)
        if test_case.url:
            st.markdown(f"🌐 **{test_case.url}**")
            # Botón para abrir la URL
            st.markdown(f'<a href="{test_case.url}" target="_blank">Abrir en nueva pestaña</a>', unsafe_allow_html=True)
        else:
            st.info("No hay URL definida para este caso de prueba.")

        st.markdown('<hr>', unsafe_allow_html=True)

        # Instrucciones
        st.markdown('<h4>Instrucciones</h4>', unsafe_allow_html=True)
        if test_case.instrucciones:
            st.markdown(test_case.instrucciones)
        else:
            st.info("No hay instrucciones definidas para este caso de prueba.")

        st.markdown('<hr>', unsafe_allow_html=True)

        # Historia de Usuario
        st.markdown('<h4>Historia de Usuario</h4>', unsafe_allow_html=True)
        if test_case.historia_de_usuario:
            st.markdown(test_case.historia_de_usuario)
        else:
            st.info("No hay historia de usuario definida para este caso de prueba.")

        st.markdown('<hr>', unsafe_allow_html=True)

        # Gherkin
        if test_case.gherkin:
            st.markdown('<h4>Especificación Gherkin</h4>', unsafe_allow_html=True)
            st.code(test_case.gherkin, language="gherkin")
            st.markdown('<hr>', unsafe_allow_html=True)

        # Código de automatización
        if hasattr(test_case, 'code') and test_case.code:
            st.markdown('<h4>Código de Automatización</h4>', unsafe_allow_html=True)

            # Determinar el lenguaje para resaltado de sintaxis
            code_language = "python"  # Por defecto
            if hasattr(test_case, 'framework'):
                if test_case.framework == "Cypress (JavaScript)":
                    code_language = "javascript"
                elif test_case.framework == "Robot Framework":
                    code_language = "robot"
                elif test_case.framework == "Selenium + Cucumber (Java)":
                    code_language = "java"

            st.code(test_case.code, language=code_language)

            # Botón para descargar el código
            if hasattr(test_case, 'framework') and test_case.framework:
                # Determinar la extensión del archivo
                file_ext = "py"  # Por defecto
                if test_case.framework == "Cypress (JavaScript)":
                    file_ext = "js"
                elif test_case.framework == "Robot Framework":
                    file_ext = "robot"
                elif test_case.framework == "Selenium + Cucumber (Java)":
                    file_ext = "java"

                st.download_button(
                    label="Descargar Código",
                    data=test_case.code,
                    file_name=f"{test_case.name.lower().replace(' ', '_')}_automation.{file_ext}",
                    mime="text/plain",
                    key="download_code_btn"
                )

            st.markdown('<hr>', unsafe_allow_html=True)

        # Historial de ejecuciones
        st.markdown('<h4>Historial de Ejecuciones</h4>', unsafe_allow_html=True)
        if test_case.history_files:
            history_data = []
            for history_file in test_case.history_files:
                try:
                    if os.path.exists(history_file):
                        with open(history_file, "r") as f:
                            history_json = json.load(f)

                            # Extraer información relevante del historial
                            execution_time = os.path.getmtime(history_file)
                            execution_date = datetime.fromtimestamp(execution_time).strftime("%Y-%m-%d %H:%M")

                            # Determinar el resultado de la ejecución
                            success = False
                            if "history" in history_json:
                                for step in history_json["history"]:
                                    if "result" in step:
                                        for result in step["result"]:
                                            if "is_done" in result and result["is_done"]:
                                                success = result.get("success", False)

                            history_data.append({
                                "Fecha": execution_date,
                                "Archivo": os.path.basename(history_file),
                                "Resultado": "Exitoso" if success else "Fallido",
                                "Ruta": history_file
                            })
                except Exception as e:
                    st.error(f"Error al cargar el archivo de historial {history_file}: {str(e)}")

            if history_data:
                df = pd.DataFrame(history_data)
                st.dataframe(df, use_container_width=True)

                # Permitir ver los detalles de una ejecución específica
                selected_history = st.selectbox(
                    "Seleccionar Ejecución para Ver Detalles",
                    options=[h["Ruta"] for h in history_data],
                    format_func=lambda x: next((f"{h['Fecha']} - {h['Resultado']}" for h in history_data if h["Ruta"] == x), x),
                    key="history_selector"
                )

                if selected_history and st.button("Ver Detalles de Ejecución", key="view_history_btn"):
                    try:
                        with open(selected_history, "r") as f:
                            history_json = json.load(f)

                            # Cargar y mostrar el historial de pruebas
                            from src.Utilities.project_manager_service import load_test_history
                            from src.UI.test_history_ui import display_test_history
                            history_data = load_test_history(selected_history)
                            if history_data:
                                st.markdown('<h3 class="glow-text">Detalles de la Ejecución</h3>', unsafe_allow_html=True)
                                display_test_history(history_data, test_case.test_id)
                            else:
                                st.error("No se pudieron cargar los detalles de la ejecución.")
                    except Exception as e:
                        st.error(f"Error al cargar los detalles de la ejecución: {str(e)}")
        else:
            st.info("No hay historial de ejecuciones para este caso de prueba.")

        # Botones de acción
        st.markdown('<hr>', unsafe_allow_html=True)
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("Ejecutar Prueba", key="run_test_btn"):
                st.session_state.run_test = {
                    "project_id": project.project_id,
                    "suite_id": suite.suite_id,
                    "test_id": test_case.test_id
                }
                st.rerun()
        with col2:
            if st.button("Editar Caso", key="edit_test_details_btn"):
                st.session_state.view_mode = "tests"
                st.session_state.edit_mode = True
                st.rerun()
        with col3:
            if st.button("Volver a la Lista", key="back_to_tests_btn"):
                st.session_state.view_mode = "tests"
                st.rerun()
