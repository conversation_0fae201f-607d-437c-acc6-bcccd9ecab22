import streamlit as st
import pandas as pd
import os
import json
import glob
import re
from datetime import datetime


def list_test_history():
    """
    Muestra una lista de todos los tests ejecutados (Smoke Test y Full Test)
    con opciones para filtrar y ver detalles.
    """
    st.markdown('<h2 class="glow-text">Historial de Tests</h2>', unsafe_allow_html=True)

    # Buscar todos los directorios de tests
    tests_dir = os.path.join("tests")
    if not os.path.exists(tests_dir):
        st.warning("No se encontraron tests ejecutados anteriormente.")
        return

    # Obtener todos los directorios de tests
    test_dirs = glob.glob(os.path.join(tests_dir, "*"))

    if not test_dirs:
        st.warning("No se encontraron tests ejecutados anteriormente.")
        return

    # Crear una lista de tests con su información básica
    test_list = []
    for test_dir in test_dirs:
        test_name = os.path.basename(test_dir)
        history_file = os.path.join(test_dir, "history.json")

        if os.path.exists(history_file):
            try:
                # Extraer información básica del archivo de historial
                with open(history_file, "r") as f:
                    history_data = json.load(f)

                # Determinar tipo de test (Smoke Test o Full Test)
                test_type = "Smoke Test" if "smoke_test" in test_name else "Full Test"

                # Verificar si el historial está vacío
                history_list = history_data.get("history", [])
                if not history_list or len(history_list) == 0:
                    # Historial vacío - usar fecha de modificación del archivo
                    test_date = datetime.fromtimestamp(
                        os.path.getmtime(history_file)
                    ).strftime("%d/%m/%Y %H:%M:%S")
                    result = "Incompleto"
                else:
                    # Extraer fecha de ejecución del primer paso
                    first_step = history_list[0]
                    step_start_time = first_step.get("metadata", {}).get("step_start_time", 0)
                    if step_start_time:
                        test_date = datetime.fromtimestamp(step_start_time).strftime("%d/%m/%Y %H:%M:%S")
                    else:
                        # Usar fecha de modificación del archivo como fallback
                        test_date = datetime.fromtimestamp(
                            os.path.getmtime(history_file)
                        ).strftime("%d/%m/%Y %H:%M:%S")

                    # Determinar resultado (éxito o error)
                    has_errors = any("error" in str(step.get("result", [])).lower()
                                    for step in history_list)
                    result = "Error" if has_errors else "Éxito"

                # Extraer URL principal si existe
                urls = []
                if history_list:  # Solo procesar si hay datos en el historial
                    for step in history_list:
                        if "state" in step and "url" in step["state"]:
                            url = step["state"]["url"]
                            if url and url != "about:blank" and url not in urls:
                                urls.append(url)

                main_url = urls[0] if urls else "N/A"

                # Contar capturas de pantalla
                screenshots_dir = os.path.join(test_dir, "screenshots")
                screenshot_count = len(glob.glob(os.path.join(screenshots_dir, "*.png"))) if os.path.exists(screenshots_dir) else 0

                # Agregar a la lista
                test_list.append({
                    "ID": test_name,
                    "Tipo": test_type,
                    "Fecha": test_date,
                    "URL": main_url,
                    "Resultado": result,
                    "Capturas": screenshot_count,
                    "Ruta": test_dir
                })
            except Exception as e:
                st.error(f"Error al procesar el historial de {test_name}: {str(e)}")

    if not test_list:
        st.warning("No se pudieron cargar los datos de los tests ejecutados.")
        return

    # Ordenar por fecha (más reciente primero)
    test_list.sort(key=lambda x: x["Fecha"], reverse=True)

    # Filtros
    st.markdown('<h3>Filtros</h3>', unsafe_allow_html=True)
    col1, col2, col3 = st.columns(3)

    with col1:
        filter_type = st.selectbox("Tipo de Test", ["Todos", "Smoke Test", "Full Test"])

    with col2:
        filter_result = st.selectbox("Resultado", ["Todos", "Éxito", "Error"])

    with col3:
        filter_text = st.text_input("Buscar por URL o ID", "")

    # Aplicar filtros
    filtered_tests = test_list

    if filter_type != "Todos":
        filtered_tests = [test for test in filtered_tests if test["Tipo"] == filter_type]

    if filter_result != "Todos":
        filtered_tests = [test for test in filtered_tests if test["Resultado"] == filter_result]

    if filter_text:
        filtered_tests = [test for test in filtered_tests
                         if filter_text.lower() in test["ID"].lower()
                         or filter_text.lower() in test["URL"].lower()]

    # Mostrar resultados
    st.markdown('<h3>Tests Ejecutados</h3>', unsafe_allow_html=True)
    st.write(f"Mostrando {len(filtered_tests)} de {len(test_list)} tests")

    # Crear DataFrame para mostrar los tests
    df = pd.DataFrame(filtered_tests)

    # Mostrar tabla con los tests
    st.dataframe(df[["ID", "Tipo", "Fecha", "URL", "Resultado", "Capturas"]], use_container_width=True)

    # Seleccionar test para ver detalles
    selected_test_id = st.selectbox("Seleccionar test para ver detalles",
                                   [f"{test['ID']} ({test['Tipo']} - {test['Fecha']})" for test in filtered_tests])

    if selected_test_id:
        # Obtener el ID del test seleccionado
        test_id = selected_test_id.split(" (")[0]

        # Encontrar el test seleccionado
        selected_test = next((test for test in filtered_tests if test["ID"] == test_id), None)

        if selected_test:
            # Cargar y mostrar los detalles del test
            history_file = os.path.join(selected_test["Ruta"], "history.json")

            if os.path.exists(history_file):
                from src.Utilities.project_manager_service import load_test_history

                # Cargar los datos del historial
                history_data = load_test_history(history_file)

                # Mostrar los detalles del test
                st.markdown(f'<h3 class="glow-text">Detalles del Test: {test_id}</h3>', unsafe_allow_html=True)

                # Agregar opción para generar código de automatización
                st.markdown('<h4 class="glow-text">Generar Código de Automatización</h4>', unsafe_allow_html=True)

                # Importar el servicio de prompts
                from src.Core.prompt_service import PromptService
                
                # Initialize PromptService
                prompt_service = PromptService()

                # Diccionario de frameworks disponibles con mapping a prompts
                def get_framework_generator(framework_name):
                    """Get the appropriate prompt generation function for a framework"""
                    framework_mapping = {
                        "Selenium + PyTest BDD (Python)": ("code-generation", "selenium-pytest"),
                        "Playwright (Python)": ("code-generation", "playwright"),
                        "Cypress (JavaScript)": ("code-generation", "cypress"),
                        "Robot Framework": ("code-generation", "robot-framework"),
                        "Selenium + Cucumber (Java)": ("code-generation", "java-selenium")
                    }
                    
                    if framework_name in framework_mapping:
                        category, prompt_name = framework_mapping[framework_name]
                        return lambda **kwargs: prompt_service.get_prompt(category, prompt_name, **kwargs)
                    else:
                        raise ValueError(f"Framework '{framework_name}' not supported")

                # Diccionario de frameworks disponibles
                FRAMEWORK_GENERATORS = {
                    "Selenium + PyTest BDD (Python)": get_framework_generator("Selenium + PyTest BDD (Python)"),
                    "Playwright (Python)": get_framework_generator("Playwright (Python)"),
                    "Cypress (JavaScript)": get_framework_generator("Cypress (JavaScript)"),
                    "Robot Framework": get_framework_generator("Robot Framework"),
                    "Selenium + Cucumber (Java)": get_framework_generator("Selenium + Cucumber (Java)")
                }

                # Seleccionar framework
                selected_framework = st.selectbox(
                    "Seleccionar framework:",
                    list(FRAMEWORK_GENERATORS.keys()),
                    key=f"framework_select_{test_id}"
                )

                # Botón para generar código
                if st.button("Generar Código", key=f"generate_code_btn_{test_id}"):
                    with st.spinner(f"Generando código de automatización para {selected_framework}..."):
                        try:
                            # Cargar el archivo JSON original para obtener más detalles
                            with open(history_file, "r") as f:
                                original_history = json.load(f)

                            # Crear un escenario Gherkin básico si no está disponible
                            gherkin_scenario = original_history.get("gherkin_scenario", """
Feature: Test Automatizado

  Scenario: Ejecutar prueba
    Given el usuario navega a la página
    When el usuario realiza acciones
    Then la prueba se completa exitosamente
""")

                            # Preparar los datos para la generación de código
                            history_for_code = {
                                "urls": history_data.get("urls", []),
                                "action_names": [action.get("name", "") for action in history_data.get("actions", [])],
                                "detailed_actions": history_data.get("actions", []),
                                "element_xpaths": {},
                                "extracted_content": [result.get("content", "") for result in history_data.get("results", [])],
                                "errors": history_data.get("errors", []),
                                "model_actions": original_history.get("model_actions", []),
                                "execution_date": history_data.get("metadata", {}).get("start_time", ""),
                                "test_id": test_id,
                                "screenshot_paths": history_data.get("screenshots", [])
                            }

                            # Obtener la función generadora adecuada
                            generator_function = FRAMEWORK_GENERATORS[selected_framework]

                            # Generar código de automatización
                            automation_code = generator_function(gherkin_scenario, history_for_code)

                            # Mostrar el código generado
                            st.markdown('<div class="card code-container fade-in">', unsafe_allow_html=True)
                            st.markdown('<h4 class="glow-text">Código Generado:</h4>', unsafe_allow_html=True)

                            # Determinar el lenguaje para la sintaxis
                            code_language = "python"
                            if selected_framework == "Cypress (JavaScript)":
                                code_language = "javascript"
                            elif selected_framework == "Selenium + Cucumber (Java)":
                                code_language = "java"

                            st.code(automation_code, language=code_language)

                            # Extraer nombre de característica para nombrar archivo
                            feature_name = "test_automation"
                            feature_match = re.search(r"Feature:\s*(.+?)(?:\n|$)", gherkin_scenario)
                            if feature_match:
                                feature_name = feature_match.group(1).strip().replace(" ", "_").lower()

                            # Obtener extensión de archivo apropiada
                            file_ext = "py"
                            if selected_framework == "Cypress (JavaScript)":
                                file_ext = "js"
                            elif selected_framework == "Selenium + Cucumber (Java)":
                                file_ext = "java"

                            # Añadir botón de descarga
                            st.download_button(
                                label=f"Descargar Código",
                                data=automation_code,
                                file_name=f"{feature_name}_automation.{file_ext}",
                                mime="text/plain",
                            )

                            st.markdown('</div>', unsafe_allow_html=True)
                            st.markdown('<div class="status-success fade-in">¡Código de automatización generado exitosamente!</div>', unsafe_allow_html=True)

                        except Exception as e:
                            st.error(f"Error al generar código para {selected_framework}: {str(e)}")

                # Mostrar los detalles del test
                display_test_history(history_data, test_id)


def display_test_history(history_data, test_id=None):
    """
    Muestra el historial de pruebas en la interfaz de usuario con pestañas organizadas.

    Args:
        history_data: Datos procesados del historial de pruebas
        test_id: Identificador único de la prueba (opcional)
    """
    if not history_data:
        st.error("No hay datos de historial para mostrar.")
        return

    # Verificar si el historial está vacío o tiene errores
    if history_data.get("metadata", {}).get("empty_history", False):
        st.warning("⚠️ **Historial de Ejecución Vacío**")
        st.info("""
        El archivo de historial existe pero está vacío. Esto puede ocurrir cuando:
        - La ejecución se interrumpió antes de completarse
        - Hubo un error durante la ejecución que impidió guardar los datos
        - La prueba no se ejecutó correctamente

        **Recomendaciones:**
        - Ejecuta la prueba nuevamente
        - Verifica que no haya errores en la configuración
        - Revisa los logs del sistema para más detalles
        """)

        # Mostrar información básica disponible
        if history_data.get("metadata", {}).get("file_path"):
            st.code(f"Archivo: {history_data['metadata']['file_path']}")
        return

    # Verificar otros tipos de errores
    if history_data.get("metadata", {}).get("file_not_found", False):
        st.error("📁 **Archivo de Historial No Encontrado**")
        st.info("El archivo de historial especificado no existe en el sistema.")
        if history_data.get("metadata", {}).get("file_path"):
            st.code(f"Ruta esperada: {history_data['metadata']['file_path']}")
        return

    if history_data.get("metadata", {}).get("json_error", False):
        st.error("🔧 **Error de Formato en el Archivo**")
        st.info("El archivo de historial existe pero tiene un formato incorrecto o está corrupto.")
        if history_data.get("errors"):
            st.code(history_data["errors"][0])
        return

    if history_data.get("metadata", {}).get("unexpected_error", False):
        st.error("❌ **Error Inesperado**")
        st.info("Ocurrió un error inesperado al cargar el historial.")
        if history_data.get("errors"):
            st.code(history_data["errors"][0])
        return

    # Crear pestañas para organizar la información
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs(["Resultados", "Detalles", "Elementos", "URLs", "Capturas", "Metadatos"])

    with tab1:
        st.markdown('<h4 class="glow-text">Resumen de Resultados</h4>', unsafe_allow_html=True)

        # Mostrar estado general de la prueba
        success = history_data["metadata"]["success"]
        status_class = "status-success" if success else "status-error"
        status_text = "✅ Prueba completada exitosamente" if success else "❌ Prueba fallida o incompleta"
        st.markdown(f'<div class="{status_class}">{status_text}</div>', unsafe_allow_html=True)

        # Agregar JavaScript para la funcionalidad de colapsar/expandir
        st.markdown("""
        <script>
        function toggleContent(id) {
            var shortContent = document.getElementById('short_' + id);
            var fullContent = document.getElementById('full_' + id);
            var button = document.getElementById('btn_' + id);

            if (shortContent.style.display === 'none') {
                shortContent.style.display = 'block';
                fullContent.style.display = 'none';
                button.textContent = 'Mostrar más';
            } else {
                shortContent.style.display = 'none';
                fullContent.style.display = 'block';
                button.textContent = 'Mostrar menos';
            }
        }
        </script>
        """, unsafe_allow_html=True)

        # Mostrar resultados de cada paso
        st.markdown('<h5>Pasos Ejecutados</h5>', unsafe_allow_html=True)
        for i, result in enumerate(history_data["results"]):
            step = result["step"]
            content = result["content"]
            is_done = result["is_done"]
            success_step = result.get("success", False)

            # Determinar el icono y estilo según el resultado
            icon = "✅" if success_step else "ℹ️" if not is_done else "❌"

            # Determinar si el contenido es largo (más de 200 caracteres)
            is_long_content = len(content) > 200 if content else False

            # Crear un ID único para este contenido
            content_id = f"content_{i}_{step}"

            if is_long_content:
                # Mostrar solo los primeros 200 caracteres inicialmente
                short_content = content[:200] + "..." if content else ""

                st.markdown(f"""
                <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px; background-color: rgba(0,0,0,0.2); border: 1px solid var(--border);">
                    <div style="display: flex; align-items: center;">
                        <div style="margin-right: 10px; font-size: 1.2rem;">{icon}</div>
                        <div style="width: 100%;">
                            <div style="font-weight: 500;">Paso {step}</div>
                            <div id="short_{content_id}" style="font-size: 0.9rem; margin-top: 5px;">{short_content}</div>
                            <div id="full_{content_id}" style="font-size: 0.9rem; margin-top: 5px; display: none;">{content}</div>
                            <button onclick="toggleContent('{content_id}')" id="btn_{content_id}" style="margin-top: 5px; padding: 2px 8px; background-color: #4B5563; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">Mostrar más</button>
                        </div>
                    </div>
                </div>
                """, unsafe_allow_html=True)
            else:
                # Si el contenido no es largo, mostrarlo normalmente
                st.markdown(f"""
                <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px; background-color: rgba(0,0,0,0.2); border: 1px solid var(--border);">
                    <div style="display: flex; align-items: center;">
                        <div style="margin-right: 10px; font-size: 1.2rem;">{icon}</div>
                        <div>
                            <div style="font-weight: 500;">Paso {step}</div>
                            <div style="font-size: 0.9rem; margin-top: 5px;">{content}</div>
                        </div>
                    </div>
                </div>
                """, unsafe_allow_html=True)

    with tab2:
        st.markdown('<h4 class="glow-text">Acciones Detalladas</h4>', unsafe_allow_html=True)

        # Crear un DataFrame para mostrar las acciones
        if history_data["actions"]:
            # Crear una versión simplificada de los detalles para el DataFrame
            simplified_actions = []
            for a in history_data["actions"]:
                details_str = str(a["details"])
                # Acortar los detalles si son muy largos
                if len(details_str) > 100:
                    simplified_details = details_str[:100] + "..."
                else:
                    simplified_details = details_str

                simplified_actions.append({
                    "Paso": a["step"],
                    "Tipo": a["type"],
                    "Detalles": simplified_details
                })

            actions_df = pd.DataFrame(simplified_actions)
            st.dataframe(actions_df, use_container_width=True)

            # Mostrar detalles completos con opción de colapsar/expandir
            st.markdown('<h5>Detalles Completos de Acciones</h5>', unsafe_allow_html=True)
            for i, action in enumerate(history_data["actions"]):
                step = action["step"]
                action_type = action["type"]
                details = str(action["details"])

                # Determinar si los detalles son largos
                is_long_details = len(details) > 200

                # Crear un ID único para este contenido
                details_id = f"details_{i}_{step}"

                if is_long_details:
                    # Mostrar solo los primeros 200 caracteres inicialmente
                    short_details = details[:200] + "..."

                    st.markdown(f"""
                    <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px; background-color: rgba(0,0,0,0.2); border: 1px solid var(--border);">
                        <div style="font-weight: 500;">Paso {step}: {action_type}</div>
                        <div id="short_{details_id}" style="font-family: monospace; font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap;">{short_details}</div>
                        <div id="full_{details_id}" style="font-family: monospace; font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap; display: none;">{details}</div>
                        <button onclick="toggleContent('{details_id}')" id="btn_{details_id}" style="margin-top: 5px; padding: 2px 8px; background-color: #4B5563; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">Mostrar más</button>
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    # Si los detalles no son largos, mostrarlos normalmente
                    st.markdown(f"""
                    <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px; background-color: rgba(0,0,0,0.2); border: 1px solid var(--border);">
                        <div style="font-weight: 500;">Paso {step}: {action_type}</div>
                        <div style="font-family: monospace; font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap;">{details}</div>
                    </div>
                    """, unsafe_allow_html=True)
        else:
            st.info("No se registraron acciones en esta prueba.")

    with tab3:
        st.markdown('<h4 class="glow-text">Elementos Interactuados</h4>', unsafe_allow_html=True)

        # Crear un DataFrame para mostrar los elementos
        if history_data["elements"]:
            elements_df = pd.DataFrame([
                {
                    "Paso": e["step"],
                    "Tipo": e["tag_name"],
                    "XPath": e["xpath"],
                    "Atributos": str(e["attributes"])
                } for e in history_data["elements"]
            ])
            st.dataframe(elements_df, use_container_width=True)
        else:
            st.info("No se interactuó con elementos en esta prueba.")

    with tab4:
        st.markdown('<h4 class="glow-text">URLs Visitadas</h4>', unsafe_allow_html=True)

        # Crear un DataFrame para mostrar las URLs
        if history_data["urls"]:
            urls_df = pd.DataFrame([
                {
                    "Paso": u["step"],
                    "URL": u["url"],
                    "Título": u["title"]
                } for u in history_data["urls"]
            ])
            st.dataframe(urls_df, use_container_width=True)
        else:
            st.info("No se visitaron URLs en esta prueba.")

    with tab5:
        st.markdown('<h4 class="glow-text">Capturas de Pantalla</h4>', unsafe_allow_html=True)

        # Mostrar capturas de pantalla
        if "screenshots" in history_data and history_data["screenshots"]:
            screenshot_paths = history_data["screenshots"]

            # Crear columnas para mostrar las capturas de pantalla
            num_screenshots = len(screenshot_paths)
            cols_per_row = 2  # Número de columnas por fila

            # Mostrar capturas de pantalla en filas de 2 columnas
            for i in range(0, num_screenshots, cols_per_row):
                cols = st.columns(cols_per_row)
                for j in range(cols_per_row):
                    if i + j < num_screenshots:
                        with cols[j]:
                            screenshot_path = screenshot_paths[i + j]
                            try:
                                # Cargar y mostrar la imagen
                                st.markdown(f"##### Captura {i + j + 1}")
                                st.image(screenshot_path, caption=f"Paso {i + j + 1}", use_container_width=True)

                                # Añadir botón de descarga si el archivo existe
                                if os.path.exists(screenshot_path):
                                    with open(screenshot_path, "rb") as f:
                                        image_bytes = f.read()
                                        st.download_button(
                                            label=f"📥 Descargar",
                                            data=image_bytes,
                                            file_name=os.path.basename(screenshot_path),
                                            mime="image/png",
                                            key=f"download_screenshot_{i+j}"
                                        )
                            except Exception as e:
                                st.error(f"Error al cargar la imagen {screenshot_path}: {str(e)}")
        else:
            st.info("No se capturaron imágenes durante la ejecución.")

    with tab6:
        st.markdown('<h4 class="glow-text">Metadatos de la Prueba</h4>', unsafe_allow_html=True)

        # Mostrar metadatos
        start_time = datetime.fromtimestamp(history_data["metadata"]["start_time"]).strftime("%Y-%m-%d %H:%M:%S") if history_data["metadata"]["start_time"] else "Desconocido"
        end_time = datetime.fromtimestamp(history_data["metadata"]["end_time"]).strftime("%Y-%m-%d %H:%M:%S") if history_data["metadata"]["end_time"] else "Desconocido"
        duration = round(history_data["metadata"]["end_time"] - history_data["metadata"]["start_time"], 2) if history_data["metadata"]["end_time"] and history_data["metadata"]["start_time"] else "Desconocido"

        metadata_html = f"""
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
            <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
                <div style="font-weight: 600; margin-bottom: 5px;">ID de Prueba</div>
                <div>{test_id or "No especificado"}</div>
            </div>
            <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
                <div style="font-weight: 600; margin-bottom: 5px;">Total de Pasos</div>
                <div>{history_data["metadata"]["total_steps"]}</div>
            </div>
            <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
                <div style="font-weight: 600; margin-bottom: 5px;">Hora de Inicio</div>
                <div>{start_time}</div>
            </div>
            <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
                <div style="font-weight: 600; margin-bottom: 5px;">Hora de Finalización</div>
                <div>{end_time}</div>
            </div>
            <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
                <div style="font-weight: 600; margin-bottom: 5px;">Duración (segundos)</div>
                <div>{duration}</div>
            </div>
            <div style="padding: 15px; background-color: var(--card); border-radius: var(--radius); border: 1px solid var(--border);">
                <div style="font-weight: 600; margin-bottom: 5px;">Estado</div>
                <div style="color: {'#10B981' if history_data['metadata']['success'] else '#EF4444'}">
                    {"Exitoso" if history_data["metadata"]["success"] else "Fallido"}
                </div>
            </div>
        </div>
        """
        st.markdown(metadata_html, unsafe_allow_html=True)
