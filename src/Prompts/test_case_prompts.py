"""Test case generation prompts in multiple languages."""

from typing import Dict

prompts: Dict[str, Dict[str, str]] = {
    "test_case_manual": {
        "en": """Your task is to generate detailed manual test cases from the following improved user story.

Improved user story:
{enhanced_story}

Please generate complete manual test cases following these guidelines:

1. Generate at least 5-10 test cases covering all acceptance criteria and main flows
2. Include positive and negative test cases
3. Each test case must include:
   - Test Case ID (format TC-001, TC-002, etc.)
   - Test Case Title (brief description of the test objective)
   - Preconditions (necessary prerequisites)
   - Test Steps (detailed numbered steps to execute the case)
   - Expected Results (what should happen)
   - Priority (High, Medium, Low)

IMPORTANT: Return the response as a valid JSON array. Each test case should be a JSON object with the following structure:
{{
  "id": "TC-001",
  "title": "Test case title",
  "preconditions": "Prerequisites for the test",
  "instrucciones": "Detailed step-by-step instructions",
  "expected_results": "Expected outcomes",
  "priority": "High/Medium/Low",
  "historia_de_usuario": "Related user story context"
}}

Do not include any introductory text, explanations, or markdown formatting. Return only the clean JSON array.""",

        "es": """Tu tarea es generar casos de prueba manuales detallados a partir de la siguiente historia de usuario mejorada.

Historia de usuario mejorada:
{enhanced_story}

Por favor, genera casos de prueba manuales completos siguiendo estas directrices:

1. Genera al menos 5-10 casos de prueba que cubran todos los criterios de aceptación y flujos principales
2. Incluye casos de prueba positivos y negativos
3. Cada caso de prueba debe incluir:
   - Test Case ID (formato TC-001, TC-002, etc.)
   - Test Case Title (breve descripción del objetivo del caso)
   - Preconditions (condiciones previas necesarias)
   - Test Steps (pasos detallados numerados para ejecutar el caso)
   - Expected Results (resultado esperado después de ejecutar los pasos)
   - Priority (High, Medium, Low)

IMPORTANTE: Devuelve la respuesta como un array JSON válido. Cada caso de prueba debe ser un objeto JSON con la siguiente estructura:
{{
  "id": "TC-001",
  "title": "Título del caso de prueba",
  "preconditions": "Prerrequisitos para la prueba",
  "instrucciones": "Instrucciones detalladas paso a paso",
  "expected_results": "Resultados esperados",
  "priority": "High/Medium/Low",
  "historia_de_usuario": "Contexto de la historia de usuario relacionada"
}}

No incluyas texto introductorio, explicaciones o formato markdown. Devuelve únicamente el array JSON limpio."""
    }
}