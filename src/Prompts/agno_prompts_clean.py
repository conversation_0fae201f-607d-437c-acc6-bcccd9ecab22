"""
Prompts unificados para agentes de QA - Versión consolidada que preserva todas las funcionalidades críticas.
Este archivo unifica agno_prompts.py y agno_prompts_new.py sin pérdida de features.
"""

import re
from typing import Dict, Any, List, Tuple, Optional
import json
import os
from langchain_google_genai import ChatGoogleGenerativeAI

# Constantes para evitar duplicación
URL_REGEX_PATTERN = r'https?://[-\w.]+(:\d+)?(/[-\w%!$&\'()*+,;=:@/~]*)?(\?[-\w%!$&\'()*+,;=:@/~]*)?'
DEFAULT_MODEL = "gemini-2.0-flash"
DEFAULT_URL = "https://example.com"

# Importaciones opcionales para mayor robustez
try:
    import streamlit as st
    STREAMLIT_AVAILABLE = True
except ImportError:
    STREAMLIT_AVAILABLE = False
    class MockStreamlit:
        class session_state:
            test_url = None
            @classmethod
            def get(cls, key, default=None):
                return getattr(cls, key, default)
            @classmethod
            def __contains__(cls, key):
                return hasattr(cls, key)
    st = MockStreamlit()

try:
    from src.Utilities.utils import extract_selectors_from_history, analyze_actions
    UTILS_AVAILABLE = True
except ImportError:
    UTILS_AVAILABLE = False
    def extract_selectors_from_history(history):
        return history.get("element_xpaths", {})
    def analyze_actions(history):
        return history.get("action_names", [])

# ============================================================================
# FUNCIONES AUXILIARES CRÍTICAS
# ============================================================================

def extract_code_content(text: str) -> str:
    """Extract code from markdown code blocks if present"""
    code_pattern = re.compile(r"```(?:python|gherkin|javascript|java|robot|markdown)?\n(.*?)```", re.DOTALL)
    match = code_pattern.search(text)
    return match.group(1).strip() if match else text.strip()

def _extract_urls_from_text(text: str) -> List[str]:
    """Extract all URLs from given text"""
    url_pattern = re.compile(URL_REGEX_PATTERN)
    return url_pattern.findall(text)

def _get_preserved_urls(gherkin_steps: str, history_data: Dict[str, Any]) -> Tuple[str, List[str]]:
    """Get preserved URLs from Gherkin scenario and session"""
    gherkin_urls = _extract_urls_from_text(gherkin_steps)
    history_urls = history_data.get('urls', [])
    
    session_url = ""
    if STREAMLIT_AVAILABLE and 'test_url' in st.session_state and st.session_state.test_url:
        session_url = st.session_state.test_url
    
    all_urls = list(set(gherkin_urls + history_urls + ([session_url] if session_url else [])))
    all_urls = [url for url in all_urls if url]
    
    # Determine base URL with priority: session > history > gherkin
    base_url = session_url or (history_urls[0] if history_urls else (gherkin_urls[0] if gherkin_urls else DEFAULT_URL))
    
    return base_url, all_urls

def _preserve_urls_in_text(text: str, original_text: str) -> str:
    """Preserve URLs from original text in processed text"""
    original_urls = _extract_urls_from_text(original_text)
    
    for url in original_urls:
        if url not in text:
            if "## URLs Originales" not in text:
                text += "\n\n## URLs Originales\n"
            text += f"- {url}\n"
    
    return text

def _get_enhanced_context(history: Dict[str, Any]) -> Dict[str, Any]:
    """Get enhanced context from history with fallbacks"""
    context = {
        'element_xpaths': history.get("element_xpaths", {}),
        'urls': history.get("urls", []),
        'action_names': history.get("action_names", []),
        'extracted_content': history.get('extracted_content', [])
    }
    
    # Try to get enhanced selectors and actions if utils are available
    try:
        if UTILS_AVAILABLE:
            context['selectors'] = extract_selectors_from_history(history)
            context['actions'] = analyze_actions(history)
        else:
            context['selectors'] = context['element_xpaths']
            context['actions'] = context['action_names']
    except Exception:
        context['selectors'] = context['element_xpaths']
        context['actions'] = context['action_names']
    
    return context

# ============================================================================
# FUNCIONES PRINCIPALES
# ============================================================================

def enhance_user_story(user_story: str) -> str:
    """Enhance user story with URL preservation"""
    llm = ChatGoogleGenerativeAI(
        model=os.getenv("LLM_MODEL", DEFAULT_MODEL),
        api_key=os.environ.get("GOOGLE_API_KEY")
    )
    
    prompt = f"""
    Tu tarea es mejorar la siguiente historia de usuario para que sea más clara, completa y siga el formato estándar 'Como [rol], quiero [funcionalidad] para [beneficio]'.
    
    Asegúrate de incluir:
    1. El rol específico del usuario (Quién)
    2. La funcionalidad deseada (Qué)
    3. El beneficio o valor esperado (Por qué)
    4. Criterios de aceptación claros y específicos
    5. Ejemplos concretos si es posible
    
    Historia original:
    {user_story}
    
    Proporciona una versión mejorada y expandida que mantenga la intención original pero sea más detallada y útil para los equipos de desarrollo y pruebas.
    """
    
    response = llm.invoke(prompt)
    enhanced_content = response.content
    
    # Preserve URLs from original story
    enhanced_content = _preserve_urls_in_text(enhanced_content, user_story)
    
    return enhanced_content

def generate_manual_test_cases(enhanced_story: str) -> str:
    """Generate manual test cases from enhanced user story"""
    llm = ChatGoogleGenerativeAI(
        model=os.getenv("LLM_MODEL", DEFAULT_MODEL),
        api_key=os.environ.get("GOOGLE_API_KEY")
    )
    
    prompt = f"""
    Tu tarea es generar casos de prueba manuales detallados a partir de la siguiente historia de usuario mejorada.
    
    Historia de usuario mejorada:
    {enhanced_story}
    
    Por favor, genera casos de prueba manuales completos siguiendo estas directrices:
    
    1. Genera al menos 5-10 casos de prueba que cubran todos los criterios de aceptación y flujos principales.
    2. Incluye casos de prueba positivos y negativos.
    3. Utiliza un formato de tabla con las siguientes columnas:
       - Test Case ID (formato TC-001, TC-002, etc.)
       - Test Case Title (breve descripción del objetivo del caso)
       - Preconditions (condiciones previas necesarias)
       - Test Steps (pasos detallados numerados para ejecutar el caso)
       - Expected Results (resultado esperado después de ejecutar los pasos)
       - Test Type (Functional, UI, Performance, etc.)
       - Priority (High, Medium, Low)
    
    Presenta la información en una tabla de Markdown bien formateada.
    """
    
    response = llm.invoke(prompt)
    test_cases_content = response.content
    
    # Preserve URLs from enhanced story
    test_cases_content = _preserve_urls_in_text(test_cases_content, enhanced_story)
    
    return test_cases_content

def generate_gherkin_scenarios(manual_test_cases_markdown: str) -> str:
    """Generate Gherkin scenarios from manual test cases with URL preservation"""
    llm = ChatGoogleGenerativeAI(
        model=os.getenv("LLM_MODEL", DEFAULT_MODEL),
        api_key=os.environ.get("GOOGLE_API_KEY")
    )
    
    # Extract URLs for preservation
    original_urls = _extract_urls_from_text(manual_test_cases_markdown)
    url_preservation_instructions = ""
    if original_urls:
        url_preservation_instructions = "\n\nIMPORTANTE: Preserva exactamente estas URLs:\n"
        for url in original_urls:
            url_preservation_instructions += f"- {url}\n"
    
    prompt = f"""
    Tu tarea es convertir los siguientes casos de prueba manuales en escenarios Gherkin bien estructurados.
    
    Casos de prueba manuales:
    {manual_test_cases_markdown}
    
    Convierte estos casos de prueba en escenarios Gherkin siguiendo estas directrices:
    
    1. Usa las keywords: Feature, Scenario, Given, When, Then, And, But
    2. Crea un Feature que represente la funcionalidad general
    3. Crea escenarios individuales para cada caso de prueba
    4. Mantiene los pasos claros, concisos y en formato imperativo presente
    5. Conserva las mismas precondiciones, acciones y resultados esperados
    6. Usa lenguaje orientado al negocio, no detalles técnicos de implementación
    7. Si hay tablas de ejemplos o escenarios outline, úsalos adecuadamente
    8. Conserva cualquier URL que aparezca en los casos de prueba originales
    9. Representa adecuadamente los casos de prueba negativos
    
    {url_preservation_instructions}
    
    Genera escenarios Gherkin completos y bien formateados que reflejen todos los casos de prueba proporcionados.
    """
    
    response = llm.invoke(prompt)
    gherkin_content = response.content
    
    # Ensure URLs are preserved in the final output
    for url in original_urls:
        if url not in gherkin_content:
            gherkin_content = f"# URL original: {url}\n" + gherkin_content
    
    return gherkin_content

def generate_selenium_pytest_bdd(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Selenium Python code with PyTest BDD with enhanced context preservation"""
    llm = ChatGoogleGenerativeAI(
        model=os.getenv("LLM_MODEL", DEFAULT_MODEL),
        api_key=os.environ.get("GOOGLE_API_KEY")
    )
    
    context = _get_enhanced_context(history)
    base_url, all_urls = _get_preserved_urls(gherkin_scenario, history)
    
    prompt = f"""
    Genera código de automatización usando Selenium con Python y PyTest BDD basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URL Base: {base_url}
    - URLs disponibles: {all_urls}
    - XPaths de elementos: {json.dumps(context['selectors'], indent=2)}
    - Acciones realizadas: {json.dumps(context['actions'], indent=2)}
    - Contenido extraído: {json.dumps(context['extracted_content'], indent=2)}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar el patrón Page Object Model
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Incluir configuración para ejecución en diferentes navegadores
    
    Proporciona una implementación completa y funcional.
    """
    
    response = llm.invoke(prompt)
    return response.content

def generate_playwright_python(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Playwright Python code with enhanced context"""
    llm = ChatGoogleGenerativeAI(
        model=os.getenv("LLM_MODEL", DEFAULT_MODEL),
        api_key=os.environ.get("GOOGLE_API_KEY")
    )
    
    context = _get_enhanced_context(history)
    base_url, all_urls = _get_preserved_urls(gherkin_scenario, history)
    
    prompt = f"""
    Genera código de automatización usando Playwright con Python basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URL Base: {base_url}
    - URLs disponibles: {all_urls}
    - XPaths de elementos: {json.dumps(context['selectors'], indent=2)}
    - Acciones realizadas: {json.dumps(context['actions'], indent=2)}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar las capacidades asíncronas de Playwright
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Aprovechar las ventajas de Playwright sobre Selenium
    
    Proporciona una implementación completa y funcional.
    """
    
    response = llm.invoke(prompt)
    return response.content

def generate_cypress_js(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Cypress JavaScript code with enhanced context"""
    llm = ChatGoogleGenerativeAI(
        model=os.getenv("LLM_MODEL", DEFAULT_MODEL),
        api_key=os.environ.get("GOOGLE_API_KEY")
    )
    
    context = _get_enhanced_context(history)
    base_url, all_urls = _get_preserved_urls(gherkin_scenario, history)
    
    prompt = f"""
    Genera código de automatización usando Cypress con JavaScript basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URL Base: {base_url}
    - URLs disponibles: {all_urls}
    - XPaths de elementos: {json.dumps(context['selectors'], indent=2)}
    - Acciones realizadas: {json.dumps(context['actions'], indent=2)}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin usando cucumber/gherkin para Cypress
    2. Utilizar comandos y aserciones propias de Cypress
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente data-* attributes o CSS)
    6. Aprovechar las capacidades de Cypress para testing de UI
    
    Proporciona una implementación completa y funcional incluyendo los archivos de configuración necesarios.
    """
    
    response = llm.invoke(prompt)
    return response.content

def generate_robot_framework(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Robot Framework code with enhanced context"""
    llm = ChatGoogleGenerativeAI(
        model=os.getenv("LLM_MODEL", DEFAULT_MODEL),
        api_key=os.environ.get("GOOGLE_API_KEY")
    )
    
    context = _get_enhanced_context(history)
    base_url, all_urls = _get_preserved_urls(gherkin_scenario, history)
    
    prompt = f"""
    Genera código de automatización usando Robot Framework basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URL Base: {base_url}
    - URLs disponibles: {all_urls}
    - XPaths de elementos: {json.dumps(context['selectors'], indent=2)}
    - Acciones realizadas: {json.dumps(context['actions'], indent=2)}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar keywords de SeleniumLibrary
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Aprovechar la sintaxis declarativa de Robot Framework
    
    Proporciona una implementación completa y funcional.
    """
    
    response = llm.invoke(prompt)
    return response.content

def generate_java_selenium(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Java Selenium with Cucumber code with enhanced context"""
    llm = ChatGoogleGenerativeAI(
        model=os.getenv("LLM_MODEL", DEFAULT_MODEL),
        api_key=os.environ.get("GOOGLE_API_KEY")
    )
    
    context = _get_enhanced_context(history)
    base_url, all_urls = _get_preserved_urls(gherkin_scenario, history)
    
    prompt = f"""
    Genera código de automatización usando Selenium con Java y Cucumber basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URL Base: {base_url}
    - URLs disponibles: {all_urls}
    - XPaths de elementos: {json.dumps(context['selectors'], indent=2)}
    - Acciones realizadas: {json.dumps(context['actions'], indent=2)}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar el patrón Page Object Model
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Incluir estructura de proyecto Maven o Gradle
    
    Proporciona una implementación completa y funcional incluyendo todos los archivos necesarios (glue code, step definitions, etc).
    """
    
    response = llm.invoke(prompt)
    return response.content

# ============================================================================
# FUNCIONES DE COMPATIBILIDAD BACKWARD
# ============================================================================

def enhance_user_story_advanced(user_story: str) -> str:
    """Alias for enhance_user_story for backward compatibility"""
    return enhance_user_story(user_story)

def generate_manual_test_cases_advanced(enhanced_story: str) -> str:
    """Alias for generate_manual_test_cases for backward compatibility"""
    return generate_manual_test_cases(enhanced_story)
