"""
Prompts unificados para agentes de QA - Versión consolidada que preserva todas las funcionalidades críticas.
Este archivo unifica agno_prompts.py y agno_prompts_new.py sin pérdida de features.
"""

import re
from typing import Dict, Any, List, Tuple
import json
import os
from langchain_google_genai import ChatGoogleGenerativeAI

# Importaciones opcionales para evitar errores en entornos sin Streamlit
try:
    import streamlit as st
    STREAMLIT_AVAILABLE = True
except ImportError:
    STREAMLIT_AVAILABLE = False
    class MockStreamlit:
        class session_state:
            test_url = None
            @classmethod
            def get(cls, key, default=None):
                return getattr(cls, key, default)
            @classmethod
            def __contains__(cls, key):
                return hasattr(cls, key)
    st = MockStreamlit()

# Importaciones de utilidades (también opcionales para mayor robustez)
try:
    from src.Utilities.utils import (
        extract_selectors_from_history,
        analyze_actions
    )
    UTILS_AVAILABLE = True
except ImportError:
    UTILS_AVAILABLE = False
    def extract_selectors_from_history(history):
        return history.get("element_xpaths", {})
    def analyze_actions(history):
        return history.get("action_names", [])

# ============================================================================
# FUNCIONES AUXILIARES CRÍTICAS (Del archivo legacy)
# ============================================================================

def extract_code_content(text: str) -> str:
    """Extract code from markdown code blocks if present"""
    # Look for content between triple backticks with optional language identifier
    code_block_pattern = re.compile(r"```(?:python|gherkin|javascript|java|robot|markdown)?\n(.*?)```", re.DOTALL)
    match = code_block_pattern.search(text)

    if match:
        return match.group(1).strip()
    return text.strip()

def _get_preserved_urls(gherkin_steps: str, history_data: Dict[str, Any]) -> Tuple[str, List[str]]:
    """
    Función auxiliar para obtener las URLs preservadas del escenario Gherkin y la sesión.
    Retorna la URL base y una lista de todas las URLs encontradas.
    """
    # Extraer todas las URLs del escenario Gherkin
    url_pattern = re.compile(
        r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[-\w%!$&\'()*+,;=:@/~]+)*(?:\?[-\w%!$&\'()*+,;=:@/~]*)?(?:#[-\w%!$&\'()*+,;=:@/~]*)?'
    )
    gherkin_urls = url_pattern.findall(gherkin_steps)

    # Obtener URLs del historial
    history_urls = history_data.get('urls', [])

    # Obtener URL de la sesión
    session_url = ""
    if 'test_url' in st.session_state and st.session_state.test_url:
        session_url = st.session_state.test_url

    # Combinar todas las URLs encontradas
    all_urls = list(set(gherkin_urls + history_urls + ([session_url] if session_url else [])))
    all_urls = [url for url in all_urls if url]  # Eliminar URLs vacías

    # Determinar la URL base
    base_url = "https://example.com"  # URL por defecto

    # Prioridad: 1. URL de sesión, 2. Primera URL del historial, 3. Primera URL del Gherkin
    if session_url:
        base_url = session_url
    elif history_urls:
        base_url = history_urls[0]
    elif gherkin_urls:
        base_url = gherkin_urls[0]

    return base_url, all_urls

def _preserve_urls_in_text(text: str, original_text: str) -> str:
    """
    Preserva las URLs del texto original en el texto procesado.
    """
    url_pattern = re.compile(
        r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[-\w%!$&\'()*+,;=:@/~]+)*(?:\?[-\w%!$&\'()*+,;=:@/~]*)?(?:#[-\w%!$&\'()*+,;=:@/~]*)?'
    )
    
    # Encontrar todas las URLs en el texto original
    original_urls = url_pattern.findall(original_text)
    
    # Verificar si alguna URL no está presente en el texto procesado
    for url in original_urls:
        if url not in text:
            # Si la URL no está en el texto procesado, añadirla en una sección especial
            if "## URLs Originales" not in text:
                text += "\n\n## URLs Originales\n"
            text += f"- {url}\n"
    
    return text

# ============================================================================
# FUNCIONES PRINCIPALES (Funcionalidad completa sin agentes rotos)
# ============================================================================

def enhance_user_story(user_story: str) -> str:
    """Mejora una historia de usuario para hacerla más clara y completa.
    
    Args:
        user_story: Historia de usuario original
        
    Returns:
        str: Historia de usuario mejorada con URLs preservadas
    """
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    prompt = f"""
    Tu tarea es mejorar la siguiente historia de usuario para que sea más clara, completa y siga el formato estándar 'Como [rol], quiero [funcionalidad] para [beneficio]'.
    
    Asegúrate de incluir:
    1. El rol específico del usuario (Quién)
    2. La funcionalidad deseada (Qué)
    3. El beneficio o valor esperado (Por qué)
    4. Criterios de aceptación claros y específicos
    5. Ejemplos concretos si es posible
    6. IMPORTANTE: Preserva exactamente cualquier URL que aparezca en la historia original
    
    Historia original:
    {user_story}
    
    Proporciona una versión mejorada y expandida que mantenga la intención original pero sea más detallada y útil para los equipos de desarrollo y pruebas.
    """
    
    # Llamar al modelo para mejorar la historia
    response = llm.invoke(prompt)
    enhanced_content = response.content
    
    # Preservar URLs del texto original
    enhanced_content = _preserve_urls_in_text(enhanced_content, user_story)
    
    return enhanced_content

def generate_manual_test_cases(enhanced_story: str) -> str:
    """Genera casos de prueba manuales a partir de una historia de usuario mejorada.
    
    Args:
        enhanced_story: Historia de usuario mejorada
        
    Returns:
        str: Casos de prueba manuales en formato markdown con URLs preservadas
    """
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    prompt = f"""
    Tu tarea es generar casos de prueba manuales detallados a partir de la siguiente historia de usuario mejorada.
    
    Historia de usuario mejorada:
    {enhanced_story}
    
    Por favor, genera casos de prueba manuales completos siguiendo estas directrices:
    
    1. Genera al menos 5-10 casos de prueba que cubran todos los criterios de aceptación y flujos principales.
    2. Incluye casos de prueba positivos y negativos.
    3. Utiliza un formato de tabla con las siguientes columnas:
       - Test Case ID (formato TC-001, TC-002, etc.)
       - Test Case Title (breve descripción del objetivo del caso)
       - Preconditions (condiciones previas necesarias)
       - Test Steps (pasos detallados numerados para ejecutar el caso)
       - Expected Results (resultado esperado después de ejecutar los pasos)
       - Test Type (Functional, UI, Performance, etc.)
       - Priority (High, Medium, Low)
    4. IMPORTANTE: Preserva exactamente cualquier URL que aparezca en la historia
    
    Presenta la información en una tabla de Markdown bien formateada.
    """
    
    # Llamar al modelo para generar casos de prueba manuales
    response = llm.invoke(prompt)
    test_cases_content = response.content
    
    # Preservar URLs del texto original
    test_cases_content = _preserve_urls_in_text(test_cases_content, enhanced_story)
    
    return test_cases_content

def generate_gherkin_scenarios(manual_test_cases_markdown: str) -> str:
    """Generate Gherkin scenarios from manual test cases with URL preservation"""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer todas las URLs del texto original para preservarlas
    url_pattern = re.compile(
        r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[-\w%!$&\'()*+,;=:@/~]+)*(?:\?[-\w%!$&\'()*+,;=:@/~]*)?(?:#[-\w%!$&\'()*+,;=:@/~]*)?'
    )
    original_urls = url_pattern.findall(manual_test_cases_markdown)
    
    # Añadir instrucciones específicas para preservar URLs
    url_preservation_instructions = ""
    if original_urls:
        url_preservation_instructions = "\n\nIMPORTANTE: Asegúrate de preservar exactamente las siguientes URLs en tus escenarios Gherkin:\n"
        for url in original_urls:
            url_preservation_instructions += f"- {url}\n"
    
    prompt = f"""
    Tu tarea es convertir los siguientes casos de prueba manuales en escenarios Gherkin bien estructurados.
    
    Casos de prueba manuales:
    {manual_test_cases_markdown}
    
    Convierte estos casos de prueba en escenarios Gherkin siguiendo estas directrices:
    
    1. Usa las keywords: Feature, Scenario, Given, When, Then, And, But
    2. Crea un Feature que represente la funcionalidad general
    3. Crea escenarios individuales para cada caso de prueba
    4. Mantiene los pasos claros, concisos y en formato imperativo presente
    5. Conserva las mismas precondiciones, acciones y resultados esperados
    6. Usa lenguaje orientado al negocio, no detalles técnicos de implementación
    7. Si hay tablas de ejemplos o escenarios outline, úsalos adecuadamente
    8. Conserva cualquier URL que aparezca en los casos de prueba originales
    9. Representa adecuadamente los casos de prueba negativos
    {url_preservation_instructions}
    
    Genera escenarios Gherkin completos y bien formateados que reflejen todos los casos de prueba proporcionados.
    """
    
    # Llamar al modelo para generar escenarios Gherkin
    response = llm.invoke(prompt)
    gherkin_content = response.content
    
    # Verificar que las URLs originales están preservadas
    for url in original_urls:
        if url not in gherkin_content:
            # Si la URL no está en el texto generado, añadirla en un comentario
            gherkin_content = f"# URL original que debe usarse: {url}\n" + gherkin_content
    
    # Si hay una URL en session_state, asegurarse de que esté incluida
    if 'test_url' in st.session_state and st.session_state.test_url:
        test_url = st.session_state.test_url
        if test_url not in gherkin_content:
            # Buscar líneas que comiencen con "Given" para insertar la URL
            lines = gherkin_content.split('\n')
            for i, line in enumerate(lines):
                if line.strip().startswith(('Given', 'Dado', 'Dada')):
                    # Si no hay una URL en esta línea, añadirla
                    if 'http' not in line:
                        lines[i] = line.replace(line, f"{line} {test_url}")
                        break
            gherkin_content = '\n'.join(lines)
    
    return gherkin_content

def generate_selenium_pytest_bdd(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Selenium Python code with PyTest BDD with enhanced context preservation."""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer información mejorada del historial
    element_xpaths = history.get("element_xpaths", {})
    urls = history.get("urls", [])
    action_names = history.get("action_names", [])
    
    # Usar funciones avanzadas si están disponibles
    try:
        selectors = extract_selectors_from_history(history)
        actions = analyze_actions(history)
    except:
        selectors = element_xpaths
        actions = action_names
    
    # Obtener URLs preservadas
    try:
        base_url, all_urls = _get_preserved_urls(gherkin_scenario, history)
    except:
        base_url = urls[0] if urls else "https://example.com"
        all_urls = urls
    
    prompt = f"""
    Genera código de automatización usando Selenium con Python y PyTest BDD basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URL Base: {base_url}
    - URLs disponibles: {all_urls if all_urls else ['No disponible']}
    - XPaths de elementos: {json.dumps(selectors, indent=2) if selectors else 'No disponible'}
    - Acciones realizadas: {json.dumps(actions, indent=2) if actions else 'No disponible'}
    - Contenido extraído: {json.dumps(history.get('extracted_content', []), indent=2)}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar el patrón Page Object Model
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Incluir configuración para ejecución en diferentes navegadores
    7. Preservar las URLs exactas proporcionadas
    8. Usar la información de contexto de navegación disponible
    
    Proporciona una implementación completa y funcional.
    """
    
    # Llamar al modelo para generar código
    response = llm.invoke(prompt)
    return response.content

def generate_playwright_python(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Playwright Python code with enhanced context preservation."""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer información mejorada del historial
    element_xpaths = history.get("element_xpaths", {})
    urls = history.get("urls", [])
    
    # Usar funciones avanzadas si están disponibles
    try:
        selectors = extract_selectors_from_history(history)
        actions = analyze_actions(history)
    except:
        selectors = element_xpaths
        actions = history.get("action_names", [])
    
    # Obtener URLs preservadas
    try:
        base_url, all_urls = _get_preserved_urls(gherkin_scenario, history)
    except:
        base_url = urls[0] if urls else "https://example.com"
        all_urls = urls
    
    prompt = f"""
    Genera código de automatización usando Playwright con Python basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URL Base: {base_url}
    - URLs disponibles: {all_urls if all_urls else ['No disponible']}
    - XPaths de elementos: {json.dumps(selectors, indent=2) if selectors else 'No disponible'}
    - Acciones realizadas: {json.dumps(actions, indent=2) if actions else 'No disponible'}
    - Contenido extraído: {json.dumps(history.get('extracted_content', []), indent=2)}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar las capacidades asíncronas de Playwright
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Aprovechar las ventajas de Playwright sobre Selenium
    7. Preservar las URLs exactas proporcionadas
    8. Usar la información de contexto de navegación disponible
    
    Proporciona una implementación completa y funcional.
    """
    
    # Llamar al modelo para generar código
    response = llm.invoke(prompt)
    return response.content

def generate_cypress_js(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Cypress JavaScript code with enhanced context preservation."""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer información mejorada del historial
    element_xpaths = history.get("element_xpaths", {})
    urls = history.get("urls", [])
    
    # Usar funciones avanzadas si están disponibles
    try:
        selectors = extract_selectors_from_history(history)
        actions = analyze_actions(history)
    except:
        selectors = element_xpaths
        actions = history.get("action_names", [])
    
    # Obtener URLs preservadas
    try:
        base_url, all_urls = _get_preserved_urls(gherkin_scenario, history)
    except:
        base_url = urls[0] if urls else "https://example.com"
        all_urls = urls
    
    prompt = f"""
    Genera código de automatización usando Cypress con JavaScript basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URL Base: {base_url}
    - URLs disponibles: {all_urls if all_urls else ['No disponible']}
    - XPaths de elementos: {json.dumps(selectors, indent=2) if selectors else 'No disponible'}
    - Acciones realizadas: {json.dumps(actions, indent=2) if actions else 'No disponible'}
    - Contenido extraído: {json.dumps(history.get('extracted_content', []), indent=2)}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin usando cucumber/gherkin para Cypress
    2. Utilizar comandos y aserciones propias de Cypress
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente data-* attributes o CSS)
    6. Aprovechar las capacidades de Cypress para testing de UI
    7. Preservar las URLs exactas proporcionadas
    8. Usar la información de contexto de navegación disponible
    
    Proporciona una implementación completa y funcional incluyendo los archivos de configuración necesarios.
    """
    
    # Llamar al modelo para generar código
    response = llm.invoke(prompt)
    return response.content

def generate_robot_framework(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Robot Framework code with enhanced context preservation."""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer información mejorada del historial
    element_xpaths = history.get("element_xpaths", {})
    urls = history.get("urls", [])
    
    # Usar funciones avanzadas si están disponibles
    try:
        selectors = extract_selectors_from_history(history)
        actions = analyze_actions(history)
    except:
        selectors = element_xpaths
        actions = history.get("action_names", [])
    
    # Obtener URLs preservadas
    try:
        base_url, all_urls = _get_preserved_urls(gherkin_scenario, history)
    except:
        base_url = urls[0] if urls else "https://example.com"
        all_urls = urls
    
    prompt = f"""
    Genera código de automatización usando Robot Framework basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URL Base: {base_url}
    - URLs disponibles: {all_urls if all_urls else ['No disponible']}
    - XPaths de elementos: {json.dumps(selectors, indent=2) if selectors else 'No disponible'}
    - Acciones realizadas: {json.dumps(actions, indent=2) if actions else 'No disponible'}
    - Contenido extraído: {json.dumps(history.get('extracted_content', []), indent=2)}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar keywords de SeleniumLibrary
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Aprovechar la sintaxis declarativa de Robot Framework
    7. Preservar las URLs exactas proporcionadas
    8. Usar la información de contexto de navegación disponible
    
    Proporciona una implementación completa y funcional.
    """
    
    # Llamar al modelo para generar código
    response = llm.invoke(prompt)
    return response.content

def generate_java_selenium(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Java Selenium with Cucumber code with enhanced context preservation."""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer información mejorada del historial
    element_xpaths = history.get("element_xpaths", {})
    urls = history.get("urls", [])
    
    # Usar funciones avanzadas si están disponibles
    try:
        selectors = extract_selectors_from_history(history)
        actions = analyze_actions(history)
    except:
        selectors = element_xpaths
        actions = history.get("action_names", [])
    
    # Obtener URLs preservadas
    try:
        base_url, all_urls = _get_preserved_urls(gherkin_scenario, history)
    except:
        base_url = urls[0] if urls else "https://example.com"
        all_urls = urls
    
    prompt = f"""
    Genera código de automatización usando Selenium con Java y Cucumber basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URL Base: {base_url}
    - URLs disponibles: {all_urls if all_urls else ['No disponible']}
    - XPaths de elementos: {json.dumps(selectors, indent=2) if selectors else 'No disponible'}
    - Acciones realizadas: {json.dumps(actions, indent=2) if actions else 'No disponible'}
    - Contenido extraído: {json.dumps(history.get('extracted_content', []), indent=2)}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar el patrón Page Object Model
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Incluir estructura de proyecto Maven o Gradle
    7. Preservar las URLs exactas proporcionadas
    8. Usar la información de contexto de navegación disponible
    
    Proporciona una implementación completa y funcional incluyendo todos los archivos necesarios (glue code, step definitions, etc).
    """
    
    # Llamar al modelo para generar código
    response = llm.invoke(prompt)
    return response.content

# ============================================================================
# FUNCIONES DE COMPATIBILIDAD BACKWARD
# ============================================================================

# Mantener compatibilidad con código existente que pueda usar nombres antiguos
def enhance_user_story_advanced(user_story: str) -> str:
    """Alias para enhance_user_story para compatibilidad"""
    return enhance_user_story(user_story)

def generate_manual_test_cases_advanced(enhanced_story: str) -> str:
    """Alias para generate_manual_test_cases para compatibilidad"""
    return generate_manual_test_cases(enhanced_story)
