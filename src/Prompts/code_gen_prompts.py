"""Code generation prompts in multiple languages."""

from typing import Dict

prompts: Dict[str, Dict[str, str]] = {
    "code_gen_selenium_pytest": {
        "en": """
        Generate Selenium PyTest BDD code based on the following:

        Gherkin Steps:
        ```gherkin
        {gherkin_steps}
        ```

        Agent Execution Details:
        - Base URL: {base_url}
        - Element Selectors: {selectors}
        - Actions Performed: {actions}
        - Extracted Content: {extracted_content}
        
        The code should follow these requirements:
        1. Use pytest-bdd framework to implement the Gherkin steps
        2. Implement a Page Object Model design pattern
        3. Include robust element location strategies
        4. <PERSON><PERSON> waits appropriately for dynamic elements
        5. Include proper error handling and reporting
        6. Be well-commented and maintainable
        7. Include a conftest.py with proper fixture setup
        """,
        
        "es": \"\"\"
        Genera codigo de Selenium PyTest BDD basado en lo siguiente:

        Pasos Gherkin:
        ```gherkin
        {gherkin_steps}
        ```

        Detalles de la ejecución del agente:
        - URL base: {base_url}
        - Selectores de elementos: {selectors}
        - Acciones realizadas: {actions}
        - Contenido extraído: {extracted_content}
        
        El codigo debe cumplir estos requisitos:
        1. Usar el framework pytest-bdd para implementar los pasos Gherkin
        2. Implementar un patrón de diseño Page Object Model
        3. Incluir estrategias robustas de localización de elementos
        4. Manejar esperas adecuadamente para elementos dinámicos
        5. Incluir manejo de errores y reportes apropiados
        6. Estar bien comentado y ser mantenible
        7. Incluir un conftest.py con la configuración adecuada de fixtures
        """
    },
    
    "code_gen_playwright": {
        "en": """
        Generate Playwright Python code based on the following:

        Gherkin Steps:
        ```gherkin
        {gherkin_steps}
        ```

        Agent Execution Details:
        - Base URL: {base_url}
        - Element Selectors: {selectors}
        - Actions Performed: {actions}
        - Extracted Content: {extracted_content}
        
        The code should follow these requirements:
        1. Use Playwright's Python API with async/await syntax
        2. Implement a Page Object Model design pattern
        3. Utilize Playwright's auto-waiting capabilities
        4. Include proper error handling and reporting
        5. Support multiple browsers (Chromium, Firefox, WebKit)
        6. Be well-commented and maintainable
        7. Include setup and teardown routines
        """,
        
        "es": \"\"\"
        Genera CODIGO de Playwright con Python basado en lo siguiente:

        Pasos Gherkin:
        ```gherkin
        {gherkin_steps}
        ```

        Detalles de la ejecución del agente:
        - URL base: {base_url}
        - Selectores de elementos: {selectors}
        - Acciones realizadas: {actions}
        - Contenido extraído: {extracted_content}
        
        El codigo debe cumplir estos requisitos:
        1. Usar la API de Python de Playwright con sintaxis async/await
        2. Implementar un patrón de diseño Page Object Model
        3. Utilizar las capacidades de auto-espera de Playwright
        4. Incluir manejo de errores y reportes apropiados
        5. Soportar múltiples navegadores (Chromium, Firefox, WebKit)
        6. Estar bien comentado y ser mantenible
        7. Incluir rutinas de configuración y limpieza
        """
    },
    
    "code_gen_cypress": {
        "en": """
        Generate Cypress JavaScript code based on the following:

        Gherkin Steps:
        ```gherkin
        {gherkin_steps}
        ```

        Agent Execution Details:
        - Base URL: {base_url}
        - Element Selectors: {selectors}
        - Actions Performed: {actions}
        - Extracted Content: {extracted_content}
        
        The code should follow these requirements:
        1. Use Cypress with Cucumber/Gherkin integration
        2. Include a Page Object Model pattern
        3. Leverage Cypress's built-in waiting and retrying capabilities
        4. Include custom commands where appropriate
        5. Configure proper reporting and screenshots
        6. Be well-commented and maintainable
        7. Include cypress.json configuration
        """,
        
        "es": \"\"\"
        Genera codigo JavaScript de Cypress basado en lo siguiente:

        Pasos Gherkin:
        ```gherkin
        {gherkin_steps}
        ```

        Detalles de la ejecución del agente:
        - URL base: {base_url}
        - Selectores de elementos: {selectors}
        - Acciones realizadas: {actions}
        - Contenido extraído: {extracted_content}
        
        El codigo debe cumplir estos requisitos:
        1. Usar Cypress con integración de Cucumber/Gherkin
        2. Incluir un patrón de Page Object Model
        3. Aprovechar las capacidades incorporadas de espera y reintento de Cypress
        4. Incluir comandos personalizados donde sea apropiado
        5. Configurar informes y capturas de pantalla adecuados
        6. Estar bien comentado y ser mantenible
        7. Incluir configuración de cypress.json
        """
    },
    
    "code_gen_robot": {
        "en": """
        Generate Robot Framework code based on the following:

        Gherkin Steps:
        ```gherkin
        {gherkin_steps}
        ```

        Agent Execution Details:
        - Base URL: {base_url}
        - Element Selectors: {selectors}
        - Actions Performed: {actions}
        - Extracted Content: {extracted_content}
        
        The code should follow these requirements:
        1. Use Robot Framework with SeleniumLibrary
        2. Create reusable keywords for common actions
        3. Organize test cases to match Gherkin scenarios
        4. Include proper setup and teardown procedures
        5. Handle waits appropriately for dynamic elements
        6. Include error handling and logging
        7. Be well-commented and maintainable
        """,
        
        "es": \"\"\"
        Genera codigo de Robot Framework basado en lo siguiente:

        Pasos Gherkin:
        ```gherkin
        {gherkin_steps}
        ```

        Detalles de la ejecución del agente:
        - URL base: {base_url}
        - Selectores de elementos: {selectors}
        - Acciones realizadas: {actions}
        - Contenido extraído: {extracted_content}
        
        El codigo debe cumplir estos requisitos:
        1. Usar Robot Framework con SeleniumLibrary
        2. Crear keywords reutilizables para acciones comunes
        3. Organizar casos de prueba para que coincidan con los escenarios Gherkin
        4. Incluir procedimientos adecuados de configuración y limpieza
        5. Manejar esperas adecuadamente para elementos dinámicos
        6. Incluir manejo de errores y registro
        7. Estar bien comentado y ser mantenible
        """
    },
    
    "code_gen_java_selenium": {
        "en": """
        Generate Java Selenium Cucumber code based on the following:

        Gherkin Steps:
        ```gherkin
        {gherkin_steps}
        ```

        Agent Execution Details:
        - Base URL: {base_url}
        - Element Selectors: {selectors}
        - Actions Performed: {actions}
        - Extracted Content: {extracted_content}
        
        The code should follow these requirements:
        1. Use Java with Selenium WebDriver and Cucumber
        2. Implement a Page Object Model design pattern
        3. Use Maven or Gradle for project structure
        4. Include robust element location strategies
        5. Handle waits appropriately for dynamic elements
        6. Include proper error handling and reporting
        7. Be well-commented and maintainable
        8. Support multiple browsers
        """,
        
        "es": \"\"\"
        Genera codigo Java de Selenium con Cucumber basado en lo siguiente:

        Pasos Gherkin:
        ```gherkin
        {gherkin_steps}
        ```

        Detalles de la ejecución del agente:
        - URL base: {base_url}
        - Selectores de elementos: {selectors}
        - Acciones realizadas: {actions}
        - Contenido extraído: {extracted_content}
        
        El codigo debe cumplir estos requisitos:
        1. Usar Java con Selenium WebDriver y Cucumber
        2. Implementar un patrón de diseño Page Object Model
        3. Usar Maven o Gradle para la estructura del proyecto
        4. Incluir estrategias robustas de localización de elementos
        5. Manejar esperas adecuadamente para elementos dinámicos
        6. Incluir manejo de errores e informes apropiados
        7. Estar bien comentado y ser mantenible
        8. Soportar múltiples navegadores
        """
    }
}