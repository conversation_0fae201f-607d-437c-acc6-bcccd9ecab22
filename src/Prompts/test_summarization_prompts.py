"""Test result summarization prompts in multiple languages."""

from typing import Dict

prompts: Dict[str, Dict[str, str]] = {
    "test_summarize_results": {
        "en": """You are an AI assistant specialized in analyzing and summarizing test execution results. Your task is to create a clear, concise, and insightful summary of the provided test results.

Test Results to Analyze:
{test_results}

Please provide a comprehensive summary that includes:

1. **Overall Test Status**: Did the tests pass or fail overall?
2. **Key Findings**: What are the main outcomes or discoveries from the test execution?
3. **Failures and Issues**: If there were any failures, what were the specific problems encountered?
4. **Success Highlights**: What worked well or passed successfully?
5. **Recommendations**: Based on the results, what actions should be taken next?
6. **Risk Assessment**: Are there any critical issues that need immediate attention?

Guidelines for the summary:
- Be concise but thorough
- Use clear, non-technical language that stakeholders can understand
- Highlight the most important information first
- Provide actionable insights
- If there are multiple test cases, group similar issues together
- Include specific error messages or failure details when relevant
- Suggest next steps or remediation actions

Format the response as a well-structured summary that can be easily shared with team members and stakeholders.""",

        "es": """Eres un asistente de IA especializado en analizar y resumir resultados de ejecución de pruebas. Tu tarea es crear un resumen claro, conciso y perspicaz de los resultados de prueba proporcionados.

Resultados de Prueba a Analizar:
{test_results}

Por favor, proporciona un resumen completo que incluya:

1. **Estado General de las Pruebas**: ¿Las pruebas pasaron o fallaron en general?
2. **Hallazgos Clave**: ¿Cuáles son los principales resultados o descubrimientos de la ejecución de pruebas?
3. **Fallas y Problemas**: Si hubo alguna falla, ¿cuáles fueron los problemas específicos encontrados?
4. **Aspectos Exitosos**: ¿Qué funcionó bien o pasó exitosamente?
5. **Recomendaciones**: Basándose en los resultados, ¿qué acciones se deben tomar a continuación?
6. **Evaluación de Riesgo**: ¿Hay algún problema crítico que necesite atención inmediata?

Directrices para el resumen:
- Sé conciso pero completo
- Usa lenguaje claro y no técnico que los interesados puedan entender
- Destaca la información más importante primero
- Proporciona ideas accionables
- Si hay múltiples casos de prueba, agrupa problemas similares
- Incluye mensajes de error específicos o detalles de fallas cuando sea relevante
- Sugiere próximos pasos o acciones de remediación

Formatea la respuesta como un resumen bien estructurado que pueda ser fácilmente compartido con miembros del equipo y partes interesadas."""
    }
}
