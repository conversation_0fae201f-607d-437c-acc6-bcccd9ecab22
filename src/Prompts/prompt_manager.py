"""Centralized prompt management system with language support.

DEPRECATED: This module is deprecated in favor of the new markdown-based prompt system.
Use src.Core.prompt_service.PromptService instead.
"""

import os
import warnings
from typing import Dict, Literal, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Language settings
LanguageType = Literal["en", "es"]
DEFAULT_LANGUAGE = os.getenv("PROMPT_LANGUAGE", "en")

class PromptManager:
    """Manages prompts with language support.

    DEPRECATED: Use src.Core.prompt_service.PromptService instead.
    This class is maintained for backward compatibility only.
    """

    def __init__(self):
        """Initialize PromptManager with deprecation warning."""
        warnings.warn(
            "PromptManager is deprecated. Use src.Core.prompt_service.PromptService instead.",
            DeprecationWarning,
            stacklevel=2
        )

        # Initialize the new prompt service for fallback
        from src.Core.prompt_service import PromptService
        self._prompt_service = PromptService()

    @staticmethod
    def get_prompt(prompt_id: str, language: Optional[LanguageType] = None) -> str:
        """Get a prompt in the specified language.

        DEPRECATED: Use PromptService.get_prompt() instead.

        Args:
            prompt_id: The unique identifier for the prompt
            language: Language code ('en' or 'es'). If None, uses environment default

        Returns:
            str: The prompt text in the requested language
        """
        warnings.warn(
            "PromptManager.get_prompt() is deprecated. Use PromptService.get_prompt() instead.",
            DeprecationWarning,
            stacklevel=2
        )

        # Use provided language or default from environment
        lang = language or DEFAULT_LANGUAGE

        # Map legacy prompt IDs to new system
        category_mapping = {
            "user_story_": "user-story",
            "test_case_": "test-cases",
            "gherkin_": "test-cases",
            "browser_": "browser-automation",
            "code_gen_": "code-generation",
            "test_summarize_": "test-analysis"
        }

        prompt_mapping = {
            "user_story_enhance": ("user-story", "enhance"),
            "test_case_generate": ("test-cases", "manual-generation"),
            "gherkin_convert": ("test-cases", "gherkin-conversion"),
            "browser_task": ("browser-automation", "task-generation"),
            "code_gen_selenium_pytest": ("code-generation", "selenium-pytest"),
            "code_gen_playwright": ("code-generation", "playwright"),
            "code_gen_cypress": ("code-generation", "cypress"),
            "code_gen_robot_framework": ("code-generation", "robot-framework"),
            "code_gen_java_selenium": ("code-generation", "java-selenium"),
            "test_summarize_results": ("test-analysis", "results-summary")
        }

        # Try direct mapping first
        if prompt_id in prompt_mapping:
            category, prompt_name = prompt_mapping[prompt_id]
            from src.Core.prompt_service import PromptService
            service = PromptService()
            return service.get_prompt(category, prompt_name, lang)

        # Try prefix mapping
        for prefix, category in category_mapping.items():
            if prompt_id.startswith(prefix):
                # Extract prompt name from ID
                prompt_name = prompt_id[len(prefix):]
                from src.Core.prompt_service import PromptService
                service = PromptService()
                try:
                    return service.get_prompt(category, prompt_name, lang)
                except Exception:
                    pass

        raise ValueError(f"Unknown prompt ID: {prompt_id}. Please migrate to the new prompt system.")