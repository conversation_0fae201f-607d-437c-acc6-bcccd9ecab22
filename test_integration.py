#!/usr/bin/env python3
"""
Integration test to validate the prompt system migration.
Tests all major functions through PromptService.
"""

import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_prompt_service_integration():
    """Test the PromptService integration with all main functions."""
    print("🧪 Testing PromptService Integration...")
    
    try:
        from src.Core.prompt_service import PromptService
        print("✅ PromptService import successful")
        
        # Initialize the service
        prompt_service = PromptService()
        print("✅ PromptService initialization successful")
        
        # Test user story enhancement
        test_story = "As a user, I want to login to the system"
        result = prompt_service.enhance_user_story(test_story)
        print(f"✅ User story enhancement: {len(result)} characters generated")
        
        # Test manual test case generation
        result = prompt_service.generate_manual_test_cases(test_story)
        print(f"✅ Manual test cases: {len(result)} characters generated")
        
        # Test Gherkin generation
        result = prompt_service.generate_gherkin_scenarios("Test case: Login functionality")
        print(f"✅ Gherkin scenarios: {len(result)} characters generated")
        
        # Test browser task generation
        result = prompt_service.generate_browser_task("Given I visit the login page", "en")
        print(f"✅ Browser task: {len(result)} characters generated")
        
        # Test code generation functions
        gherkin_scenario = """
        Feature: Login
        Scenario: User logs in successfully
          Given I am on the login page
          When I enter valid credentials
          Then I should be logged in
        """
        
        history = {"element_xpaths": {"login_button": "//button[@id='login']"}}
        
        # Test all code generation functions
        frameworks = [
            ("selenium_pytest_bdd", "Selenium PyTest BDD"),
            ("playwright_python", "Playwright Python"),
            ("cypress_js", "Cypress JavaScript"),
            ("robot_framework", "Robot Framework"),
            ("java_selenium", "Java Selenium")
        ]
        
        for method_name, framework_name in frameworks:
            try:
                method = getattr(prompt_service, f"generate_{method_name}")
                result = method(gherkin_scenario, history)
                print(f"✅ {framework_name}: {len(result)} characters generated")
            except Exception as e:
                print(f"⚠️  {framework_name}: Error - {e}")
        
        print("\n🎉 All PromptService integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def test_app_integration():
    """Test that the main app.py functions work."""
    print("\n🧪 Testing app.py Integration...")
    
    try:
        from app import generate_gherkin_scenarios, enhance_user_story, generate_manual_test_cases
        print("✅ app.py imports successful")
        
        # Test wrapper functions
        result = enhance_user_story(user_story="Test story")
        print(f"✅ app.py enhance_user_story: {len(result)} characters")
        
        result = generate_manual_test_cases(enhanced_story="Enhanced test story")
        print(f"✅ app.py generate_manual_test_cases: {len(result)} characters")
        
        result = generate_gherkin_scenarios(manual_test_cases_markdown="Test cases")
        print(f"✅ app.py generate_gherkin_scenarios: {len(result)} characters")
        
        print("🎉 app.py integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ app.py integration test failed: {e}")
        return False

def test_browser_helper_integration():
    """Test that browser_helper.py imports work."""
    print("\n🧪 Testing browser_helper.py Integration...")
    
    try:
        from src.Utilities.browser_helper import BrowserHelperConfig
        print("✅ browser_helper.py import successful")
        
        # Test that the import doesn't fail (browser-use might not be installed)
        config = BrowserHelperConfig()
        print("✅ BrowserHelperConfig creation successful")
        
        print("🎉 browser_helper.py integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ browser_helper.py integration test failed: {e}")
        return False

def main():
    """Run all integration tests."""
    print("=" * 60)
    print("🚀 PROMPT SYSTEM MIGRATION INTEGRATION TESTS")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 3
    
    if test_prompt_service_integration():
        tests_passed += 1
    
    if test_app_integration():
        tests_passed += 1
        
    if test_browser_helper_integration():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 RESULTS: {tests_passed}/{total_tests} test suites passed")
    
    if tests_passed == total_tests:
        print("🎉 ALL INTEGRATION TESTS PASSED!")
        print("✅ Migration completed successfully!")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit(main())
