#!/usr/bin/env python3
"""
Examples demonstrating the improved browser helper capabilities.
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from src.Utilities.browser_helper import (
    create_and_run_agent,
    BrowserHelperConfig,
    create_fast_config,
    create_robust_config,
    create_secure_config,
    create_debug_config,
    validate_config
)
from src.Utilities.utils import controller


async def example_basic_usage():
    """Example 1: Basic usage (backward compatible)"""
    print("=== Example 1: Basic Usage ===")
    
    scenario = """
    Given I am on https://www.google.com
    When I search for "browser automation"
    Then I should see search results
    """
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            language="en"
        )
        
        print(f"✅ Execution completed successfully")
        print(f"📊 Actions executed: {len(history.action_names())}")
        print(f"🌐 URLs visited: {len(history.urls())}")
        
    except Exception as e:
        print(f"❌ Execution failed: {e}")


async def example_custom_configuration():
    """Example 2: Custom configuration with advanced features"""
    print("\n=== Example 2: Custom Configuration ===")
    
    # Create custom configuration
    config = BrowserHelperConfig(
        model_provider="gemini",
        headless=True,  # Run in headless mode
        use_vision=True,
        enable_memory=True,
        deterministic_rendering=True,
        highlight_elements=True,
        viewport_expansion=1000,
        max_steps=100,
        save_conversation_path="./conversations",
        generate_gif=False  # Disable GIF for this example
    )
    
    # Validate configuration
    warnings = validate_config(config)
    if warnings:
        print(f"⚠️  Configuration warnings: {warnings}")
    
    scenario = """
    Given I am on https://httpbin.org/forms/post
    When I fill in the form with test data
    And I submit the form
    Then I should see a success response
    """
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=config,
            language="en"
        )
        
        print(f"✅ Execution completed with custom config")
        print(f"📊 Actions executed: {len(history.action_names())}")
        if history.errors():
            print(f"⚠️  Errors encountered: {len(history.errors())}")
        
    except Exception as e:
        print(f"❌ Execution failed: {e}")


async def example_predefined_configurations():
    """Example 3: Using predefined configurations"""
    print("\n=== Example 3: Predefined Configurations ===")
    
    scenario = """
    Given I am on https://httpbin.org/
    When I click on the "HTTP Methods" link
    Then I should see the HTTP methods page
    """
    
    # Example with fast configuration
    print("🚀 Testing with fast configuration...")
    fast_config = create_fast_config()
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=fast_config,
            language="en"
        )
        print(f"✅ Fast execution completed in {len(history.action_names())} actions")
        
    except Exception as e:
        print(f"❌ Fast execution failed: {e}")
    
    # Example with robust configuration
    print("\n🛡️  Testing with robust configuration...")
    robust_config = create_robust_config(
        headless=True,  # Override default for this example
        generate_gif=False  # Disable GIF for example
    )
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=robust_config,
            language="en"
        )
        print(f"✅ Robust execution completed in {len(history.action_names())} actions")
        
    except Exception as e:
        print(f"❌ Robust execution failed: {e}")


async def example_secure_configuration():
    """Example 4: Secure configuration with domain restrictions"""
    print("\n=== Example 4: Secure Configuration ===")
    
    # Create secure configuration with domain restrictions
    secure_config = create_secure_config(
        allowed_domains=["https://httpbin.org", "https://*.httpbin.org"],
        headless=True
    )
    
    scenario = """
    Given I am on https://httpbin.org/
    When I navigate to the status codes section
    Then I should see different HTTP status codes
    """
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=secure_config,
            language="en"
        )
        
        print(f"✅ Secure execution completed")
        print(f"🔒 Domain restrictions enforced")
        print(f"📊 Actions executed: {len(history.action_names())}")
        
    except Exception as e:
        print(f"❌ Secure execution failed: {e}")


async def example_multi_model_support():
    """Example 5: Testing different model providers"""
    print("\n=== Example 5: Multi-Model Support ===")
    
    scenario = """
    Given I am on https://httpbin.org/html
    When I examine the page content
    Then I should see HTML content
    """
    
    # Test with different providers (if API keys are available)
    providers = []
    
    if os.getenv("GOOGLE_API_KEY"):
        providers.append(("gemini", "gemini-2.0-flash"))
    
    if os.getenv("OPENAI_API_KEY"):
        providers.append(("openai", "gpt-4o"))
    
    if os.getenv("ANTHROPIC_API_KEY"):
        providers.append(("anthropic", "claude-3-5-sonnet-20240620"))
    
    for provider, model in providers:
        print(f"\n🤖 Testing with {provider} ({model})...")
        
        config = BrowserHelperConfig(
            model_provider=provider,
            model_name=model,
            headless=True,
            use_vision=True,
            enable_memory=False,  # Disable for quick test
            max_steps=20
        )
        
        try:
            history = await create_and_run_agent(
                scenario_text=scenario,
                controller_instance=controller,
                config=config,
                language="en"
            )
            
            print(f"✅ {provider} execution completed in {len(history.action_names())} actions")
            
        except Exception as e:
            print(f"❌ {provider} execution failed: {e}")


async def example_debug_mode():
    """Example 6: Debug mode for development"""
    print("\n=== Example 6: Debug Mode ===")
    
    # Create debug configuration
    debug_config = create_debug_config(
        headless=True,  # Override for automated testing
        save_conversation_path="./debug_conversations",
        max_failures=1,
        generate_gif=False  # Disable for example
    )
    
    scenario = """
    Given I am on https://httpbin.org/
    When I look for the "Forms" section
    Then I should find form-related endpoints
    """
    
    try:
        history = await create_and_run_agent(
            scenario_text=scenario,
            controller_instance=controller,
            config=debug_config,
            language="en"
        )
        
        print(f"✅ Debug execution completed")
        print(f"🐛 Conversation saved to debug_conversations/")
        print(f"📊 Actions executed: {len(history.action_names())}")
        
        # Show detailed action information
        for i, action in enumerate(history.action_names()[:5]):  # Show first 5 actions
            print(f"  {i+1}. {action}")
        
        if len(history.action_names()) > 5:
            print(f"  ... and {len(history.action_names()) - 5} more actions")
        
    except Exception as e:
        print(f"❌ Debug execution failed: {e}")


async def main():
    """Run all examples"""
    print("🚀 Browser Helper Improvement Examples")
    print("=" * 50)
    
    # Check for required API key
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ GOOGLE_API_KEY environment variable not found")
        print("Please set your Google API key to run these examples")
        return
    
    examples = [
        example_basic_usage,
        example_custom_configuration,
        example_predefined_configurations,
        example_secure_configuration,
        example_multi_model_support,
        example_debug_mode
    ]
    
    for example in examples:
        try:
            await example()
        except Exception as e:
            print(f"❌ Example failed: {e}")
        
        # Small delay between examples
        await asyncio.sleep(1)
    
    print("\n🎉 All examples completed!")
    print("\nNext steps:")
    print("- Review the generated conversation logs")
    print("- Experiment with different configurations")
    print("- Check the documentation in docs/browser_helper_improvements.md")


if __name__ == "__main__":
    asyncio.run(main())
