#!/usr/bin/env python3
"""
Ejemplo de uso de las mejoras del browser helper:
1. URL como parámetro
2. Memoria de browser-use
3. Initial actions optimizadas
4. Configuraciones mejoradas
"""

import asyncio
import os
from src.Utilities.test_executor import TestExecutor

def example_smoke_test_with_url():
    """Ejemplo de smoke test usando URL como parámetro"""
    print("🧪 Ejemplo: Smoke Test con URL como parámetro")
    print("=" * 50)
    
    # Crear el executor
    executor = TestExecutor(
        api_key=os.getenv("GOOGLE_API_KEY"),
        language="es"
    )
    
    # Escenario Gherkin simple
    scenario = """
    Feature: Verificación básica de sitio web
    
    Scenario: Verificar que la página principal carga
        Given que el usuario está en la página principal
        When la página se carga completamente
        Then el usuario debe ver el contenido principal
    """
    
    # URL directa como parámetro (no necesita extraerse del Gherkin)
    url = "https://example.com"
    
    print(f"📍 URL: {url}")
    print(f"📝 Escenario: {scenario.strip()}")
    
    try:
        # Ejecutar smoke test con URL como parámetro
        result = executor.run_smoke_test(scenario, url=url)
        
        print(f"\n✅ Resultado: {'Éxito' if result.get('success') else 'Fallo'}")
        print(f"🆔 Test ID: {result.get('test_id')}")
        
        if result.get('screenshot_paths'):
            print(f"📸 Screenshots: {len(result['screenshot_paths'])} archivos")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def example_full_test_with_memory():
    """Ejemplo de test completo con memoria de browser-use"""
    print("\n🧪 Ejemplo: Test Completo con Memoria")
    print("=" * 50)
    
    # Crear el executor
    executor = TestExecutor(
        api_key=os.getenv("GOOGLE_API_KEY"),
        language="es"
    )
    
    # Escenario más complejo que se beneficia de memoria
    scenario = """
    Feature: Navegación compleja con múltiples pasos
    
    Scenario: Búsqueda y navegación en sitio web
        Given que el usuario está en la página principal
        When el usuario busca "información importante"
        And hace clic en el primer resultado
        And navega a la sección de detalles
        Then debe encontrar la información relevante
        And debe poder regresar a la página anterior
    """
    
    url = "https://example.com"
    
    print(f"📍 URL: {url}")
    print(f"📝 Escenario complejo con memoria habilitada")
    
    try:
        # Ejecutar test completo con memoria habilitada
        result = executor.run_full_test(scenario, url=url)
        
        print(f"\n✅ Resultado: {'Éxito' if result.get('success') else 'Fallo'}")
        print(f"🆔 Test ID: {result.get('test_id')}")
        
        if result.get('summary'):
            summary = result['summary']
            print(f"📊 Resumen:")
            print(f"  - Total escenarios: {summary.get('total_scenarios', 0)}")
            print(f"  - Exitosos: {summary.get('successful_scenarios', 0)}")
            print(f"  - Fallidos: {summary.get('failed_scenarios', 0)}")
        
        if result.get('screenshot_paths'):
            print(f"📸 Screenshots: {len(result['screenshot_paths'])} archivos")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_configuration_examples():
    """Mostrar ejemplos de configuraciones mejoradas"""
    print("\n⚙️ Ejemplos de Configuraciones Mejoradas")
    print("=" * 50)
    
    from src.Utilities.browser_helper import create_fast_config, create_robust_config
    
    # Configuración para smoke tests
    print("🚀 Configuración para Smoke Tests:")
    fast_config = create_fast_config(
        headless=True,
        use_vision=True,
        enable_memory=False,  # Deshabilitada para velocidad
        max_steps=30
    )
    print(f"  - Vision: {fast_config.use_vision}")
    print(f"  - Memory: {fast_config.enable_memory}")
    print(f"  - Max steps: {fast_config.max_steps}")
    print(f"  - Initial actions: Auto-generadas desde URL")
    
    # Configuración para tests completos
    print("\n🔧 Configuración para Tests Completos:")
    robust_config = create_robust_config(
        enable_memory=True,  # Habilitada para contexto
        memory_agent_id="complex_test_agent",
        memory_interval=10,
        max_steps=150
    )
    print(f"  - Vision: {robust_config.use_vision}")
    print(f"  - Memory: {robust_config.enable_memory}")
    print(f"  - Memory agent ID: {robust_config.memory_agent_id}")
    print(f"  - Memory interval: {robust_config.memory_interval}")
    print(f"  - Max steps: {robust_config.max_steps}")

def show_improvements_summary():
    """Mostrar resumen de mejoras implementadas"""
    print("\n🎉 Resumen de Mejoras Implementadas")
    print("=" * 50)
    
    improvements = [
        ("❌ Error use_planner corregido", "Compatible con browser-use 0.2.5+"),
        ("✅ URL como parámetro", "execute_smoke_test(scenario, url=url)"),
        ("✅ Memoria de browser-use", "MemoryConfig integrado para tests complejos"),
        ("✅ Initial actions optimizadas", "Navegación directa sin procesamiento LLM"),
        ("✅ Configuraciones mejoradas", "Fast para smoke, robust para tests completos"),
        ("✅ Compatibilidad hacia atrás", "Código existente sigue funcionando")
    ]
    
    for improvement, description in improvements:
        print(f"  {improvement}: {description}")

def main():
    """Ejecutar ejemplos de las mejoras"""
    print("🔧 Ejemplos de Mejoras del Browser Helper")
    print("=" * 60)
    
    # Mostrar configuraciones
    show_configuration_examples()
    
    # Mostrar resumen de mejoras
    show_improvements_summary()
    
    print("\n" + "=" * 60)
    print("💡 Para ejecutar los tests reales:")
    print("  1. Configura GOOGLE_API_KEY en tu entorno")
    print("  2. Descomenta las llamadas a los ejemplos abajo")
    print("  3. Ejecuta: python examples/browser_improvements_example.py")
    
    # Descomentar para ejecutar tests reales:
    # success1 = example_smoke_test_with_url()
    # success2 = example_full_test_with_memory()
    # 
    # if success1 and success2:
    #     print("\n🎉 Todos los ejemplos ejecutados exitosamente!")
    # else:
    #     print("\n⚠️ Algunos ejemplos fallaron - revisa la configuración")

if __name__ == "__main__":
    main()
