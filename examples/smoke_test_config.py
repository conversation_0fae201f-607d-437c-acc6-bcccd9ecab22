#!/usr/bin/env python3
"""
Ejemplo específico de configuración para Smoke Tests

Este archivo demuestra la configuración optimizada para smoke tests:
- ✅ Vision habilitada
- ✅ Planner habilitado
- ✅ Memoria avanzada deshabilitada (para velocidad)
"""

import asyncio
import os
from src.Utilities.browser_helper import (
    create_and_run_agent,
    create_fast_config,
    validate_config
)
from src.Utilities.utils import controller


async def smoke_test_example():
    """Ejemplo de smoke test con configuración optimizada"""
    print("🔥 Ejecutando Smoke Test con configuración optimizada")
    print("=" * 60)

    # Crear configuración específica para smoke tests
    smoke_config = create_fast_config(
        # Configuración base
        headless=True,
        max_steps=30,

        # ✅ Configuración específica para smoke tests
        use_vision=True,        # Habilitado para mejor detección de elementos
        enable_memory=False,    # NO usar memoria avanzada (más rápido)

        # Configuración de timing optimizada para velocidad
        minimum_wait_page_load_time=0.3,
        wait_for_network_idle_page_load_time=0.5,
        maximum_wait_page_load_time=8.0,
        wait_between_actions=0.2,

        # Configuración visual optimizada
        viewport_expansion=300,
        highlight_elements=False,  # Desactivar para velocidad
        deterministic_rendering=True
    )

    # Validar configuración
    print("🔍 Validando configuración...")
    warnings = validate_config(smoke_config)
    if warnings:
        print("⚠️ Warnings encontrados:")
        for warning in warnings:
            print(f"  - {warning}")
    else:
        print("✅ Configuración válida")

    # Mostrar configuración aplicada
    print("\n📋 Configuración aplicada:")
    print(f"  - Vision: {smoke_config.use_vision}")
    print(f"  - Memory: {smoke_config.enable_memory}")
    print(f"  - Max steps: {smoke_config.max_steps}")
    print(f"  - Headless: {smoke_config.headless}")
    print(f"  - Wait between actions: {smoke_config.wait_between_actions}s")
    print("  - Initial actions: Auto-generated from URL")

    # Escenario de smoke test
    smoke_scenario = """
    Feature: Smoke Test Básico

    Scenario: Verificar funcionalidad básica
        Given que el usuario navega a "https://example.com"
        When el usuario ve la página principal
        Then la página debe cargar correctamente
        And debe mostrar el título "Example Domain"
    """

    print(f"\n🚀 Ejecutando smoke test...")
    print("-" * 40)

    try:
        # Ejecutar smoke test con configuración optimizada
        history = await create_and_run_agent(
            scenario_text=smoke_scenario,
            controller_instance=controller,
            config=smoke_config,
            language="en"
        )

        print("✅ Smoke test completado exitosamente")

        # Mostrar resumen de ejecución
        if hasattr(history, 'action_names') and callable(getattr(history, 'action_names')):
            actions_count = len(history.action_names())
            print(f"📊 Acciones ejecutadas: {actions_count}")

        if hasattr(history, 'urls') and callable(getattr(history, 'urls')):
            urls_count = len(history.urls())
            print(f"🌐 URLs visitadas: {urls_count}")

        return True

    except Exception as e:
        print(f"❌ Error en smoke test: {e}")
        return False


async def compare_configurations():
    """Comparar diferentes configuraciones para smoke tests"""
    print("\n🔄 Comparando configuraciones...")
    print("=" * 60)

    # Configuración básica (sin optimizaciones)
    basic_config = create_fast_config(
        use_vision=False,
        enable_memory=True,  # Memoria habilitada (más lento)
        max_steps=50
    )

    # Configuración optimizada para smoke tests
    optimized_config = create_fast_config(
        use_vision=True,     # ✅ Habilitado
        enable_memory=False, # ✅ Deshabilitado para velocidad
        max_steps=30
    )

    configs = [
        ("Básica", basic_config),
        ("Optimizada para Smoke Tests", optimized_config)
    ]

    for name, config in configs:
        print(f"\n📋 Configuración {name}:")
        print(f"  - Vision: {config.use_vision}")
        print(f"  - Memory: {config.enable_memory}")
        print(f"  - Max steps: {config.max_steps}")

        warnings = validate_config(config)
        if warnings:
            print(f"  ⚠️ Warnings: {len(warnings)}")
        else:
            print("  ✅ Sin warnings")


def show_smoke_test_benefits():
    """Mostrar beneficios de la configuración optimizada"""
    print("\n💡 Beneficios de la configuración optimizada para Smoke Tests:")
    print("=" * 60)

    benefits = [
        ("✅ Vision habilitada", "Mejor detección y análisis de elementos visuales"),
        ("✅ Initial actions optimizadas", "Navegación directa a URL sin procesamiento LLM"),
        ("✅ Memoria deshabilitada", "Ejecución más rápida sin overhead de memoria"),
        ("✅ Browser-use 0.2.5+ compatible", "Sin errores de use_planner"),
        ("⚡ Timing optimizado", "Esperas mínimas pero suficientes para estabilidad"),
        ("🎯 Pasos limitados", "Ejecución enfocada en verificaciones esenciales"),
        ("🚀 Rendering determinístico", "Resultados consistentes entre ejecuciones")
    ]

    for feature, description in benefits:
        print(f"  {feature}: {description}")


async def main():
    """Función principal"""
    print("🔥 Configuración Optimizada para Smoke Tests")
    print("=" * 60)

    # Verificar variables de entorno
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ GOOGLE_API_KEY no está configurada")
        print("   Configura la variable de entorno antes de ejecutar")
        return

    # Mostrar beneficios
    show_smoke_test_benefits()

    # Comparar configuraciones
    await compare_configurations()

    # Ejecutar ejemplo de smoke test
    success = await smoke_test_example()

    print("\n" + "=" * 60)
    if success:
        print("🎉 Ejemplo de smoke test completado exitosamente")
        print("\n💡 Recomendaciones:")
        print("  - Usa create_fast_config() para smoke tests")
        print("  - Usa create_robust_config() para tests completos con memoria")
        print("  - Habilita vision para mejor funcionamiento")
        print("  - Deshabilita memoria para smoke tests (velocidad)")
        print("  - Habilita memoria para tests completos (contexto)")
        print("  - Las initial actions se generan automáticamente desde URLs")
        print("  - Pasa URL como parámetro en execute_smoke_test()")
        print("  - Ajusta max_steps según la complejidad del test")
    else:
        print("❌ Ejemplo de smoke test falló")
        print("\n🔧 Verifica:")
        print("  - Conexión a internet")
        print("  - Variables de entorno configuradas")
        print("  - Dependencias instaladas")


if __name__ == "__main__":
    asyncio.run(main())
