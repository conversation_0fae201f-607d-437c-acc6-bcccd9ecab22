"""Script para ejecutar la API web de QA Agent."""

import os
import sys
import uvicorn
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

# Verificar que la API key está configurada
if not os.environ.get("GOOGLE_API_KEY") and not os.environ.get("GEMINI_API_KEY"):
    print("Error: No se encontró la API key de Google Gemini. Por favor, configura la variable de entorno GOOGLE_API_KEY o GEMINI_API_KEY.")
    sys.exit(1)

# Configurar los parámetros de ejecución
HOST = os.environ.get("API_HOST", "0.0.0.0")  # Escuchar en todas las interfaces
PORT = int(os.environ.get("API_PORT", 8000))  # Puerto por defecto

# Punto de entrada principal
def main():
    print(f"Iniciando API web de QA Agent en http://{HOST}:{PORT}...")
    print("Documentaciu00f3n API disponible en:")
    print(f"  - Swagger UI: http://localhost:{PORT}/docs")
    print(f"  - ReDoc: http://localhost:{PORT}/redoc")
    print("\nPresiona Ctrl+C para detener el servidor.")
    
    # Ejecutar la aplicacion FastAPI con Uvicorn
    uvicorn.run(
        "src.API.api:app",
        host=HOST,
        port=PORT,
        reload=True,  # Activar recarga automática en desarrollo
        log_level="info"
    )

if __name__ == "__main__":
    main()