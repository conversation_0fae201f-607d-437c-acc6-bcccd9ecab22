"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";

import { useToast } from "@/hooks/use-toast";
import { callExecuteSmokeTest } from "@/lib/api";
import type { ExecuteSmokeTestInput, TestExecutionHistoryData } from "@/lib/types";
import { ExecutionDetailsView } from "@/components/execution/ExecutionDetailsView";
import { GherkinHighlighter } from "@/components/execution/GherkinHighlighter";
import { ManualTestCasesTable } from "@/components/execution/ManualTestCasesTable";
import { Flame, Sparkles, Copy, Save } from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { copyToClipboard } from "@/lib/clipboard";

const smokeTestPlaygroundSchema = z.object({
  baseUrl: z.string().url("A valid Base URL is required."),
  instructions: z.string().min(20, "Instructions must be at least 20 characters."),
  userStory: z.string().optional(),
});
type SmokeTestPlaygroundFormData = z.infer<typeof smokeTestPlaygroundSchema>;

function ExecutionLoadingSkeleton() {
  return (
    <div className="space-y-4 mt-6">
      <Skeleton className="h-10 w-1/3" /> {/* Header */}
      <Skeleton className="h-12 w-full rounded-md" /> {/* Tabs List */}
      <div className="mt-4 p-4 rounded-lg border bg-card">
        <Skeleton className="h-6 w-1/4 mb-4" /> {/* Tab Title */}
        <div className="space-y-4">
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    </div>
  );
}


export default function SmokeTestPlaygroundPage() {
  const { toast } = useToast();
  const [executionResult, setExecutionResult] = useState<TestExecutionHistoryData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [formDataForSave, setFormDataForSave] = useState<SmokeTestPlaygroundFormData | null>(null);
  const [enhancedUserStory, setEnhancedUserStory] = useState<string>("");
  const [manualTestCases, setManualTestCases] = useState<string[]>([]);


  const form = useForm<SmokeTestPlaygroundFormData>({
    resolver: zodResolver(smokeTestPlaygroundSchema),
    defaultValues: {
      baseUrl: "",
      instructions: "",
      userStory: "",
    },
  });

  const onExecuteSubmit = async (data: SmokeTestPlaygroundFormData) => {
    setIsLoading(true);
    setError(null);
    setExecutionResult(null);
    setFormDataForSave(data); // Store form data for potential save

    try {
      // Use the API integration to execute the smoke test
      const result = await callExecuteSmokeTest(data as ExecuteSmokeTestInput);

      // If there was a user story, create an enhanced version for editing
      if (data.userStory) {
        // Extract AI-enhanced user story from results if available
        // For demonstration, using the original until we know where to get the enhanced one
        setEnhancedUserStory(data.userStory);
      }

      // Generate sample manual test cases based on instructions
      const instructionLines = data.instructions.split('\n').filter(line => line.trim());
      const sampleTestCases = instructionLines.map(line => {
        // Remove leading numbers and periods if present
        const cleanLine = line.replace(/^\d+\.\s*/, "");
        return cleanLine;
      });

      setManualTestCases(sampleTestCases);
      setExecutionResult(result);
      toast({ title: "Smoke Test Execution Complete!" });
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred during smoke test execution.");
      toast({ title: "Error Executing Smoke Test", description: (err as Error).message, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyToClipboard = async (text: string, fieldName: string) => {
    const success = await copyToClipboard(text);
    
    if (success) {
      toast({ title: "Copied to Clipboard", description: `${fieldName} copied.` });
    } else {
      toast({ 
        title: "Copy Failed", 
        description: `Could not copy ${fieldName}. You may need to copy manually.`, 
        variant: "destructive" 
      });
    }
  };


  return (
    <div>
      <h1 className="page-header flex items-center gap-2"><Flame /> Smoke Test Playground</h1>
      <Card>
        <CardHeader>
          <CardTitle>Configure Smoke Test</CardTitle>
          <CardDescription>Provide the necessary details to simulate a smoke test using AI.</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onExecuteSubmit)} className="space-y-6">
              <FormField control={form.control} name="baseUrl" render={({ field }) => (
                <FormItem>
                  <FormLabel>Base URL</FormLabel>
                  <FormControl><Input type="url" placeholder="https://example.com" {...field} /></FormControl>
                  <FormDescription>The main URL of the application or feature to test.</FormDescription>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="instructions" render={({ field }) => (
                <FormItem>
                  <FormLabel>Instructions / Test Steps</FormLabel>
                  <FormControl><Textarea placeholder="1. Navigate to login page.\n2. Enter '<EMAIL>' in email field.\n3. Enter 'password123' in password field.\n4. Click 'Login' button.\n5. Verify 'Dashboard' text is visible." {...field} className="min-h-[150px]" /></FormControl>
                  <FormDescription>Detailed steps the AI should simulate for the smoke test.</FormDescription>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="userStory" render={({ field }) => (
                <FormItem>
                  <div className="flex items-center justify-between">
                    <FormLabel>User Story (Optional)</FormLabel>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="h-8"
                      onClick={() => field.value && setEnhancedUserStory(field.value)}
                      disabled={!field.value}
                    >
                      <Sparkles className="mr-2 h-4 w-4" />
                      Enhance with AI
                    </Button>
                  </div>
                  <FormControl><Textarea placeholder="e.g., As a new user, I want to be able to register an account successfully." {...field} className="min-h-[80px]" /></FormControl>
                  <FormDescription>Provide a user story for better context if available.</FormDescription>
                  <FormMessage />
                </FormItem>
              )} />
              <Button type="submit" disabled={isLoading} className="w-full md:w-auto">
                {isLoading ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Sparkles className="mr-2 h-4 w-4" />}
                Execute Smoke Test
              </Button>
            </form>
          </Form>

          {enhancedUserStory && (
            <div className="mt-6 border rounded-md p-4 bg-muted/20">
              <h3 className="text-lg font-semibold flex items-center mb-3">
                <Sparkles className="h-5 w-5 mr-2 text-primary"/>
                AI Enhanced User Story
              </h3>
              <Textarea
                value={enhancedUserStory}
                onChange={(e) => setEnhancedUserStory(e.target.value)}
                className="min-h-[150px] font-mono text-sm"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {isLoading && <ExecutionLoadingSkeleton />}

      {error && (
        <Alert variant="destructive" className="mt-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Execution Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {executionResult && !isLoading && (
        <div className="mt-8">
          <div className="flex justify-end mb-4">
            <Button onClick={() => setShowSaveModal(true)} variant="outline">
              <Save className="mr-2 h-4 w-4" />
              Prepare to Save Test Case
            </Button>
          </div>
          <ExecutionDetailsView historyData={executionResult} testCaseId="smoke-test-playground" />
        </div>
      )}

      {showSaveModal && formDataForSave && executionResult && (
        <Dialog open={showSaveModal} onOpenChange={setShowSaveModal}>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle>Save Test Case Details</DialogTitle>
              <DialogDescription>
                Copy the following details to manually create a new Test Case in your desired Project and Suite.
              </DialogDescription>
            </DialogHeader>
            <ScrollArea className="max-h-[60vh] p-1 pr-4">
              <div className="space-y-4 py-4">
                <div>
                  <Label className="font-semibold">Base URL</Label>
                  <div className="mt-1 flex items-center gap-2">
                    <Input readOnly value={formDataForSave.baseUrl} className="font-mono text-sm" />
                    <Button variant="ghost" size="icon" onClick={() => handleCopyToClipboard(formDataForSave.baseUrl, "Base URL")}><Copy className="h-4 w-4"/></Button>
                  </div>
                </div>
                <div>
                  <Label className="font-semibold">Instructions</Label>
                   <div className="mt-1 flex items-start gap-2">
                    <Textarea readOnly value={formDataForSave.instructions} className="font-mono text-sm min-h-[100px]" />
                    <Button variant="ghost" size="icon" onClick={() => handleCopyToClipboard(formDataForSave.instructions, "Instructions")}><Copy className="h-4 w-4"/></Button>
                  </div>
                </div>
                {formDataForSave.userStory && (
                  <div>
                    <Label className="font-semibold">User Story (Original)</Label>
                    <div className="mt-1 flex items-start gap-2">
                      <Textarea readOnly value={formDataForSave.userStory} className="font-mono text-sm min-h-[80px]" />
                      <Button variant="ghost" size="icon" onClick={() => handleCopyToClipboard(formDataForSave.userStory!, "User Story")}><Copy className="h-4 w-4"/></Button>
                    </div>
                  </div>
                )}

                {enhancedUserStory && (
                  <div className="mt-4">
                    <Label className="font-semibold">Enhanced User Story</Label>
                    <div className="mt-1 flex items-start gap-2">
                      <Textarea
                        value={enhancedUserStory}
                        onChange={(e) => setEnhancedUserStory(e.target.value)}
                        className="font-mono text-sm min-h-[120px]"
                      />
                      <Button variant="ghost" size="icon" onClick={() => handleCopyToClipboard(enhancedUserStory, "Enhanced User Story")}><Copy className="h-4 w-4"/></Button>
                    </div>
                  </div>
                )}
                {executionResult.generatedGherkin && (
                   <div className="mt-4">
                    <Label className="font-semibold">Generated Gherkin</Label>
                    <div className="mt-1">
                      <GherkinHighlighter gherkinCode={executionResult.generatedGherkin} />
                    </div>
                  </div>
                )}

                <div className="mt-4">
                  <Label className="font-semibold">Manual Test Cases</Label>
                  <div className="mt-1">
                    <ManualTestCasesTable testCases={manualTestCases.length > 0 ? manualTestCases : [
                      "Login to application with valid credentials",
                      "Navigate to dashboard",
                      "Verify all dashboard components load correctly"
                    ]} />
                  </div>
                </div>

                <p className="text-sm text-muted-foreground">
                  Remember to also provide a Name, Description, and Tags when creating the Test Case.
                </p>
              </div>
            </ScrollArea>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="secondary">Close</Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
