"use client";

import { useEffect, useState } from 'react';
import { getApiHealth } from '@/lib/api';
import type { ApiHealth } from '@/lib/types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, CheckCircle2, Server } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';

export default function DashboardPage() {
  const [apiHealth, setApiHealth] = useState<ApiHealth | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchApiHealth() {
      try {
        setLoading(true);
        const healthData = await getApiHealth();
        setApiHealth(healthData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch API health');
        setApiHealth(null);
      } finally {
        setLoading(false);
      }
    }
    fetchApiHealth();
  }, []);

  return (
    <div className="container mx-auto py-8">
      <h1 className="page-header">Dashboard</h1>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Status</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading && (
              <>
                <Skeleton className="h-8 w-24 mb-2" />
                <Skeleton className="h-4 w-full" />
              </>
            )}
            {error && (
              <div className="flex items-center text-destructive">
                <AlertCircle className="mr-2 h-5 w-5" />
                <p className="text-lg font-bold">Error</p>
              </div>
            )}
            {apiHealth && (
              <>
                <div className="flex items-center">
                  {apiHealth.status === 'online' ? (
                    <CheckCircle2 className="mr-2 h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="mr-2 h-5 w-5 text-yellow-500" />
                  )}
                  <p className="text-2xl font-bold capitalize">{apiHealth.status}</p>
                </div>
                <p className="text-xs text-muted-foreground">
                  API Version: {apiHealth.version}
                </p>
              </>
            )}
            {error && <p className="text-xs text-destructive mt-1">{error}</p>}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Key</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading && <Skeleton className="h-8 w-32" />}
            {apiHealth && (
              <Badge variant={apiHealth.api_key_configured ? 'default' : 'destructive'} className={apiHealth.api_key_configured ? 'bg-green-500 hover:bg-green-600' : ''}>
                {apiHealth.api_key_configured ? 'Configured' : 'Not Configured'}
              </Badge>
            )}
             {error && <p className="text-xs text-destructive mt-1">Could not check API key status</p>}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Server Time</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
             {loading && <Skeleton className="h-8 w-48" />}
            {apiHealth && (
              <p className="text-lg font-semibold">
                {format(new Date(apiHealth.timestamp), 'yyyy-MM-dd HH:mm:ss')}
              </p>
            )}
            {error && <p className="text-xs text-destructive mt-1">Could not fetch server time</p>}
          </CardContent>
        </Card>
      </div>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Welcome to QA Killer!</CardTitle>
          <CardDescription>
            Desarrollado con ❤️ para matar a los QA
          </CardDescription>
        </CardHeader>
        <CardContent>
      
        </CardContent>
      </Card>
    </div>
  );
}
