"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

export default function SettingsPage() {
  const [language, setLanguage] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem("language") || "es";
    }
    return "es";
  });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem("language", language);
    }
  }, [language]);

  return (
    <div>
      <h1 className="page-header">Settings</h1>
      <Card>
        <CardHeader>
          <CardTitle>Application Settings</CardTitle>
          <CardDescription>Configure your QA Killer application preferences.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="api-url">API Base URL</Label>
            <Input id="api-url" defaultValue={process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000/api"} disabled />
            <p className="text-xs text-muted-foreground">
              This is configured via environment variables (NEXT_PUBLIC_API_BASE_URL).
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="theme">Theme</Label>
            <p className="text-sm text-muted-foreground">
              Theme is currently set to dark mode by default. Theme switching can be added via AppHeader.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="language">Default Language</Label>
            <select
              id="language"
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
            </select>
          </div>
          
          {/* Add more settings here as needed */}
          
          <Button onClick={() => saveSettings()}>Save Settings</Button>
        </CardContent>
      </Card>
    </div>
  );

  function saveSettings() {
    localStorage.setItem("language", language);
  }
}
