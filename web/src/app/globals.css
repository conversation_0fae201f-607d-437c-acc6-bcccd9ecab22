@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 258 39% 51%; /* Deep Purple #624CAB */
    --primary-foreground: 0 0% 98%; /* Light text for primary */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 182 100% 74%; /* Electric Blue #7DF9FF */
    --accent-foreground: 220 10% 10%; /* Dark text for accent */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 258 39% 51%; /* Ring color based on primary */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem; /* 8px */

    /* Custom status colors */
    --success: 145 63% 49%;
    --success-foreground: 145 63% 95%;
    --error: 0 72% 51%;
    --error-foreground: 0 72% 95%;
    --warning: 38 92% 50%;
    --warning-foreground: 38 92% 95%;
    --info: 208 93% 50%;
    --info-foreground: 208 93% 95%;

    /* Sidebar colors (light theme) */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 258 39% 51%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 258 39% 51%;
  }

  .dark {
    --background: 220 10% 15%; /* Dark Gray #212529 */
    --foreground: 0 0% 98%; /* White text */
    --card: 240 8% 10%; /* #18181B */
    --card-foreground: 0 0% 98%;
    --popover: 240 8% 10%;
    --popover-foreground: 0 0% 98%;
    --primary: 258 39% 51%; /* Deep Purple #624CAB */
    --primary-foreground: 0 0% 98%; /* Light text for primary */
    --secondary: 240 4% 16%; /* Darker secondary for dark theme */
    --secondary-foreground: 0 0% 98%;
    --muted: 240 4% 16%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 182 100% 74%; /* Electric Blue #7DF9FF */
    --accent-foreground: 220 10% 10%; /* Dark text for accent */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5% 16%; /* #27272A */
    --input: 240 5% 16%;
    --ring: 182 100% 60%; /* Lighter shade of accent for focus rings */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Custom status colors (dark theme) */
    --success: 145 58% 41%;
    --success-foreground: 145 58% 95%;
    --error: 0 63% 40%;
    --error-foreground: 0 63% 95%;
    --warning: 38 82% 40%;
    --warning-foreground: 38 82% 95%;
    --info: 208 83% 40%;
    --info-foreground: 208 83% 95%;

    /* Sidebar colors (dark theme) */
    --sidebar-background: 220 10% 12%; /* Slightly lighter than main bg */
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 258 39% 51%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 16%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 5% 16%;
    --sidebar-ring: 182 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .glow-text {
    @apply text-primary font-semibold;
  }

  .status-badge {
    @apply px-2.5 py-0.5 rounded-full text-xs font-semibold;
  }
  .status-success {
    @apply bg-green-500/10 text-green-400 border border-green-500/20;
  }
  .status-error, .status-failed {
    @apply bg-red-500/10 text-red-400 border border-red-500/20;
  }
  .status-not-executed, .status-neutral {
    @apply bg-gray-500/10 text-gray-400 border border-gray-500/20;
  }
  .status-warning {
     @apply bg-yellow-500/10 text-yellow-400 border border-yellow-500/20;
  }
  .status-info {
     @apply bg-blue-500/10 text-blue-400 border border-blue-500/20;
  }

  .page-header {
    @apply text-3xl font-bold tracking-tight mb-6;
  }
  .section-header {
    @apply text-2xl font-semibold tracking-tight mb-4;
  }
  .subsection-header {
    @apply text-xl font-semibold tracking-tight mb-3;
  }

  /* Excel-like table styles */
  .excel-style-table {
    @apply w-full border-collapse;
  }

  .excel-style-table th {
    @apply bg-muted font-semibold text-sm;
  }

  .excel-row:hover {
    @apply bg-muted/30;
  }

  .excel-row [contenteditable]:hover {
    @apply bg-card ring-1 ring-border cursor-text;
  }

  .excel-row [contenteditable]:focus {
    @apply bg-background outline-none ring-1 ring-primary;
  }

  .excel-style-table th {
    @apply bg-muted/50 border font-medium px-4 py-2 text-left;
  }

  .excel-style-table td {
    @apply border px-4 py-2;
  }

  .excel-row:nth-child(even) {
    @apply bg-muted/20;
  }

  .excel-row:hover {
    @apply bg-muted/40;
  }

  /* Custom styles for prompt management */
  .prompt-card {
    transition: all 0.2s ease-in-out;
    border-left: 4px solid theme('colors.blue.500');
  }

  .prompt-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }

  .prompt-card.valid {
    border-left-color: theme('colors.green.500');
  }

  .prompt-card.invalid {
    border-left-color: theme('colors.red.500');
  }

  .prompt-card.unknown {
    border-left-color: theme('colors.amber.500');
  }

  .validation-icon {
    transition: all 0.2s ease-in-out;
  }

  .validation-icon:hover {
    transform: scale(1.1);
  }

  .category-tab {
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }

  .category-tab[data-state="active"] {
    background: theme('colors.blue.50');
    border-color: theme('colors.blue.200');
    color: theme('colors.blue.700');
  }

  .search-input:focus {
    border-color: theme('colors.blue.400');
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
  }

  .empty-state {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  }
}
