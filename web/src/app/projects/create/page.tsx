"use client";

import { useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createProject } from "@/lib/api";
import type { ProjectCreateInput } from "@/lib/types";
import { ProjectForm } from "@/components/forms/ProjectForm";
import { useToast } from "@/hooks/use-toast";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function CreateProjectPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const mutation = useMutation({
    mutationFn: (newProject: ProjectCreateInput) => createProject(newProject),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast({
        title: "Project Created",
        description: `Project "${data.name}" has been successfully created.`,
      });
      router.push(`/projects/${data.project_id}`);
    },
    onError: (error) => {
      toast({
        title: "Error Creating Project",
        description: error.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = async (data: ProjectCreateInput) => {
    await mutation.mutateAsync(data);
  };

  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href="/projects">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Projects
        </Link>
      </Button>
      <h1 className="page-header">Create New Project</h1>
      <ProjectForm 
        onSubmit={handleSubmit} 
        isSubmitting={mutation.isPending}
        submitButtonText="Create Project"
      />
    </div>
  );
}
