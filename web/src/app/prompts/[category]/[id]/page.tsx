"use client";

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Save, 
  ArrowLeft, 
  Eye, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  FileText,
  Code,
  Settings,
  Play
} from 'lucide-react';
import Link from 'next/link';
import { fetchPromptDetail, updatePrompt, validatePrompt } from '@/lib/api';

interface PromptMetadata {
  title: string;
  description: string;
  version: string;
  lastModified: string;
  tags?: string[];
  author?: string;
  language?: string;
  // Additional fields from API response
  id?: string;
  name?: string;
  displayName?: string;
  file?: string;
  languages?: string[];
  variables?: string[];
  outputs?: string[];
  examples?: any[];
}

interface PromptContent {
  [section: string]: string;
}

interface PromptDetail {
  category: string;
  prompt_id: string;
  metadata: PromptMetadata;
  content: PromptContent;
  validation?: {
    is_valid: boolean;
    errors: string[];
    warnings: string[];
  };
}

export default function PromptEditorPage() {
  const params = useParams();
  const router = useRouter();
  const category = params.category as string;
  const id = params.id as string;

  const [prompt, setPrompt] = useState<PromptDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [validating, setValidating] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState('content');
  const [validation, setValidation] = useState<any>(null);

  useEffect(() => {
    loadPrompt();
  }, [category, id]);

  const loadPrompt = async () => {
    try {
      setLoading(true);
      const data: any = await fetchPromptDetail(category, id);
      
      // Transform API response to match component interface
      const transformedData: PromptDetail = {
        category: data.category,
        prompt_id: data.prompt_id,
        metadata: {
          title: data.metadata.name || data.metadata.displayName || data.metadata.title || data.prompt_id,
          description: data.metadata.description || '',
          version: data.metadata.version || '1.0.0',
          lastModified: data.metadata.lastModified || data.metadata.created || new Date().toISOString(),
          tags: data.metadata.tags || [],
          author: data.metadata.author,
          language: data.metadata.language,
          // Keep original fields for reference
          ...data.metadata
        },
        content: data.content || {},
        validation: data.validation
      };
      
      setPrompt(transformedData);
      
      // Load validation status
      await validatePromptData(transformedData);
    } catch (error) {
      console.error('Error loading prompt:', error);
    } finally {
      setLoading(false);
    }
  };

  const validatePromptData = async (promptData?: PromptDetail) => {
    try {
      setValidating(true);
      const validationResult = await validatePrompt(category, id);
      setValidation(validationResult);
      
      if (promptData) {
        setPrompt(prev => prev ? { ...prev, validation: validationResult } : null);
      }
    } catch (error) {
      console.error('Error validating prompt:', error);
    } finally {
      setValidating(false);
    }
  };

  const savePrompt = async () => {
    if (!prompt) return;

    try {
      setSaving(true);
      await updatePrompt(category, id, {
        metadata: prompt.metadata,
        content: prompt.content
      });
      
      setHasChanges(false);
      await validatePromptData();
      
      // Show success message
      console.log('Prompt saved successfully');
    } catch (error) {
      console.error('Error saving prompt:', error);
    } finally {
      setSaving(false);
    }
  };

  const updateMetadata = (field: keyof PromptMetadata, value: any) => {
    if (!prompt) return;
    
    setPrompt(prev => ({
      ...prev!,
      metadata: {
        ...prev!.metadata,
        [field]: value
      }
    }));
    setHasChanges(true);
  };

  const updateContent = (section: string, value: string) => {
    if (!prompt) return;
    
    setPrompt(prev => ({
      ...prev!,
      content: {
        ...prev!.content,
        [section]: value
      }
    }));
    setHasChanges(true);
  };

  const addTag = (tag: string) => {
    if (!prompt || !tag.trim()) return;
    
    const currentTags = prompt.metadata.tags || [];
    if (currentTags.includes(tag.trim())) return;
    
    updateMetadata('tags', [...currentTags, tag.trim()]);
  };

  const removeTag = (tagToRemove: string) => {
    if (!prompt) return;
    
    const currentTags = prompt.metadata.tags || [];
    updateMetadata('tags', currentTags.filter(tag => tag !== tagToRemove));
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (!prompt) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <XCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Prompt not found</h3>
          <p className="text-gray-600 mb-4">The requested prompt could not be loaded.</p>
          <Link href="/prompts">
            <Button>Back to Prompts</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link href="/prompts">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{prompt.metadata.title}</h1>
            <p className="text-gray-700 text-sm font-medium">
              {category} / {id}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Validation Status */}
          {validation && (
            <div className="flex items-center gap-2">
              {validation.is_valid ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              <span className="text-sm text-gray-800 font-medium">
                {validation.is_valid ? 'Valid' : 'Has Issues'}
              </span>
            </div>
          )}
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => validatePromptData()}
            disabled={validating}
          >
            {validating ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600" />
            ) : (
              <Play className="h-4 w-4" />
            )}
            Validate
          </Button>
          
          <Link href={`/prompts/${category}/${id}/preview`}>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
          </Link>
          
          <Button 
            onClick={savePrompt}
            disabled={saving || !hasChanges}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      {/* Validation Alerts */}
      {validation && !validation.is_valid && validation.errors && validation.errors.length > 0 && (
        <Alert className="mb-6 border-red-300 bg-red-100">
          <XCircle className="h-4 w-4 text-red-600" />
          <AlertDescription>
            <div className="font-semibold mb-2 text-red-800">Validation Issues:</div>
            <ul className="list-disc list-inside space-y-1">
              {validation.errors.map((error: string, index: number) => (
                <li key={index} className="text-sm text-red-700">{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {validation && validation.warnings && validation.warnings.length > 0 && (
        <Alert className="mb-6 border-amber-300 bg-amber-100">
          <AlertCircle className="h-4 w-4 text-amber-600" />
          <AlertDescription>
            <div className="font-semibold mb-2 text-amber-800">Warnings:</div>
            <ul className="list-disc list-inside space-y-1">
              {validation.warnings.map((warning: string, index: number) => (
                <li key={index} className="text-sm text-amber-700">{warning}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Editor Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="content">
            <FileText className="h-4 w-4 mr-2" />
            Content
          </TabsTrigger>
          <TabsTrigger value="metadata">
            <Settings className="h-4 w-4 mr-2" />
            Metadata
          </TabsTrigger>
          <TabsTrigger value="raw">
            <Code className="h-4 w-4 mr-2" />
            Raw Markdown
          </TabsTrigger>
        </TabsList>

        {/* Content Editor */}
        <TabsContent value="content" className="space-y-6">
          {Object.entries(prompt.content).map(([section, content]) => (
            <Card key={section} className="border-gray-300 shadow-sm">
              <CardHeader className="bg-gray-50 border-b border-gray-200">
                <CardTitle className="text-lg capitalize text-gray-800 font-semibold">
                  {section.replace(/_/g, ' ')}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <Textarea
                  value={content}
                  onChange={(e) => updateContent(section, e.target.value)}
                  className="min-h-[200px] font-mono text-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  placeholder={`Enter ${section} content...`}
                />
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Metadata Editor */}
        <TabsContent value="metadata">
          <Card className="border-gray-300 shadow-sm">
            <CardHeader className="bg-gray-50 border-b border-gray-200">
              <CardTitle className="text-gray-800 font-semibold">Prompt Metadata</CardTitle>
              <CardDescription className="text-gray-600">
                Configure the metadata for this prompt
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={prompt.metadata.title}
                    onChange={(e) => updateMetadata('title', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="version">Version</Label>
                  <Input
                    id="version"
                    value={prompt.metadata.version}
                    onChange={(e) => updateMetadata('version', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={prompt.metadata.description}
                  onChange={(e) => updateMetadata('description', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="author">Author</Label>
                <Input
                  id="author"
                  value={prompt.metadata.author || ''}
                  onChange={(e) => updateMetadata('author', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-gray-700 font-medium">Tags</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {(prompt.metadata.tags || []).map(tag => (
                    <Badge 
                      key={tag} 
                      variant="secondary" 
                      className="cursor-pointer hover:bg-red-200 bg-gray-200 text-gray-800 border border-gray-300"
                      onClick={() => removeTag(tag)}
                    >
                      {tag} ×
                    </Badge>
                  ))}
                </div>
                <Input
                  placeholder="Add tag and press Enter"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addTag(e.currentTarget.value);
                      e.currentTarget.value = '';
                    }
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Raw Markdown */}
        <TabsContent value="raw">
          <Card className="border-gray-300 shadow-sm">
            <CardHeader className="bg-gray-50 border-b border-gray-200">
              <CardTitle className="text-gray-800 font-semibold">Raw Markdown</CardTitle>
              <CardDescription className="text-gray-600">
                View and edit the raw markdown representation
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <Textarea
                value={generateRawMarkdown()}
                onChange={(e) => parseRawMarkdown(e.target.value)}
                className="min-h-[500px] font-mono text-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                placeholder="Raw markdown content..."
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );

  function generateRawMarkdown(): string {
    if (!prompt) return '';
    
    let markdown = `---\n`;
    markdown += `title: "${prompt.metadata.title || prompt.metadata.name || ''}"\n`;
    markdown += `description: "${prompt.metadata.description || ''}"\n`;
    markdown += `version: "${prompt.metadata.version || '1.0.0'}"\n`;
    markdown += `lastModified: "${prompt.metadata.lastModified || new Date().toISOString()}"\n`;
    markdown += `tags: [${(prompt.metadata.tags || []).map(t => `"${t}"`).join(', ')}]\n`;
    if (prompt.metadata.author) {
      markdown += `author: "${prompt.metadata.author}"\n`;
    }
    markdown += `---\n\n`;
    
    Object.entries(prompt.content).forEach(([section, content]) => {
      markdown += `## ${section.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}\n\n`;
      markdown += `${content}\n\n`;
    });
    
    return markdown;
  }

  function parseRawMarkdown(markdown: string): void {
    // Simple parser for demonstration
    // In a real implementation, you'd want a more robust markdown parser
    console.log('Parsing raw markdown (not implemented in demo):', markdown);
  }
}
