"use client";

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Save,
  ArrowLeft,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Code,
  Settings,
  Play,
  HelpCircle,
  Loader2,
  Info,
  Keyboard
} from 'lucide-react';
import Link from 'next/link';
import { fetchPromptDetail, updatePrompt, validatePrompt } from '@/lib/api';

interface PromptMetadata {
  title: string;
  description: string;
  version: string;
  lastModified: string;
  tags?: string[];
  author?: string;
  language?: string;
  // Additional fields from API response
  id?: string;
  name?: string;
  displayName?: string;
  file?: string;
  languages?: string[];
  variables?: string[];
  outputs?: string[];
  examples?: any[];
}

interface PromptContent {
  [section: string]: string;
}

interface PromptDetail {
  category: string;
  prompt_id: string;
  metadata: PromptMetadata;
  content: PromptContent;
  validation?: {
    is_valid: boolean;
    errors: string[];
    warnings: string[];
  };
}

export default function PromptEditorPage() {
  const params = useParams();
  const category = params.category as string;
  const id = params.id as string;

  const [prompt, setPrompt] = useState<PromptDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [validating, setValidating] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState('content');
  const [validation, setValidation] = useState<any>(null);

  useEffect(() => {
    loadPrompt();
  }, [category, id]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault();
            if (hasChanges && !saving) {
              savePrompt();
            }
            break;
          case '1':
            event.preventDefault();
            setActiveTab('content');
            break;
          case '2':
            event.preventDefault();
            setActiveTab('metadata');
            break;
          case '3':
            event.preventDefault();
            setActiveTab('raw');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [hasChanges, saving]);

  const loadPrompt = async () => {
    try {
      setLoading(true);
      const data: any = await fetchPromptDetail(category, id);
      
      // Transform API response to match component interface
      const transformedData: PromptDetail = {
        category: data.category,
        prompt_id: data.prompt_id,
        metadata: {
          title: data.metadata.name || data.metadata.displayName || data.metadata.title || data.prompt_id,
          description: data.metadata.description || '',
          version: data.metadata.version || '1.0.0',
          lastModified: data.metadata.lastModified || data.metadata.created || new Date().toISOString(),
          tags: data.metadata.tags || [],
          author: data.metadata.author,
          language: data.metadata.language,
          // Keep original fields for reference
          ...data.metadata
        },
        content: data.content || {},
        validation: data.validation
      };
      
      setPrompt(transformedData);
      
      // Load validation status
      await validatePromptData(transformedData);
    } catch (error) {
      console.error('Error loading prompt:', error);
    } finally {
      setLoading(false);
    }
  };

  const validatePromptData = async (promptData?: PromptDetail) => {
    try {
      setValidating(true);
      const validationResult = await validatePrompt(category, id);
      setValidation(validationResult);
      
      if (promptData) {
        setPrompt(prev => prev ? { ...prev, validation: validationResult } : null);
      }
    } catch (error) {
      console.error('Error validating prompt:', error);
    } finally {
      setValidating(false);
    }
  };

  const savePrompt = async () => {
    if (!prompt) return;

    try {
      setSaving(true);
      await updatePrompt(category, id, {
        metadata: prompt.metadata,
        content: prompt.content
      });
      
      setHasChanges(false);
      await validatePromptData();
      
      // Show success message
      console.log('Prompt saved successfully');
    } catch (error) {
      console.error('Error saving prompt:', error);
    } finally {
      setSaving(false);
    }
  };

  const updateMetadata = (field: keyof PromptMetadata, value: any) => {
    if (!prompt) return;
    
    setPrompt(prev => ({
      ...prev!,
      metadata: {
        ...prev!.metadata,
        [field]: value
      }
    }));
    setHasChanges(true);
  };

  const updateContent = (section: string, value: string) => {
    if (!prompt) return;
    
    setPrompt(prev => ({
      ...prev!,
      content: {
        ...prev!.content,
        [section]: value
      }
    }));
    setHasChanges(true);
  };

  const addTag = (tag: string) => {
    if (!prompt || !tag.trim()) return;
    
    const currentTags = prompt.metadata.tags || [];
    if (currentTags.includes(tag.trim())) return;
    
    updateMetadata('tags', [...currentTags, tag.trim()]);
  };

  const removeTag = (tagToRemove: string) => {
    if (!prompt) return;
    
    const currentTags = prompt.metadata.tags || [];
    updateMetadata('tags', currentTags.filter(tag => tag !== tagToRemove));
  };

  if (loading) {
    return (
      <TooltipProvider>
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col items-center justify-center h-64 space-y-4">
            <div className="relative">
              <div className="prompt-loading-spinner h-12 w-12"></div>
              <div className="prompt-loading-pulse"></div>
            </div>
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold text-foreground">Loading Prompt</h3>
              <p className="text-sm text-muted-foreground">Please wait while we fetch the prompt details...</p>
            </div>
          </div>
        </div>
      </TooltipProvider>
    );
  }

  if (!prompt) {
    return (
      <TooltipProvider>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12 space-y-6">
            <div className="relative">
              <XCircle className="h-16 w-16 text-destructive mx-auto" />
              <div className="absolute inset-0 rounded-full bg-destructive/10 animate-pulse"></div>
            </div>
            <div className="space-y-3">
              <h3 className="text-xl font-semibold text-foreground">Prompt Not Found</h3>
              <p className="text-muted-foreground max-w-md mx-auto">
                The requested prompt could not be loaded. It may have been moved, deleted, or you may not have permission to access it.
              </p>
            </div>
            <div className="flex gap-3 justify-center">
              <Link href="/prompts">
                <Button variant="default" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Prompts
                </Button>
              </Link>
              <Button variant="outline" onClick={() => window.location.reload()} className="gap-2">
                <Play className="h-4 w-4" />
                Retry
              </Button>
            </div>
          </div>
        </div>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <div className="container mx-auto px-4 py-8 space-y-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 p-6 bg-card border border-border rounded-lg shadow-sm">
          <div className="flex items-center gap-4">
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href="/prompts">
                  <Button variant="ghost" size="sm" className="gap-2 hover:bg-accent/50 transition-colors">
                    <ArrowLeft className="h-4 w-4" />
                    Back
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <p>Return to prompts list</p>
              </TooltipContent>
            </Tooltip>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl lg:text-3xl font-bold text-foreground">{prompt.metadata.title}</h1>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-5 w-5 text-muted-foreground hover:text-foreground transition-colors" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-sm">
                    <p>Edit and manage this AI prompt. Use the tabs below to modify content, metadata, or view raw markdown.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="text-muted-foreground text-sm font-medium flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">{category}</Badge>
                <span>/</span>
                <code className="text-xs bg-muted px-2 py-1 rounded">{id}</code>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2 flex-wrap">
            {/* Validation Status */}
            {validation && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2 px-3 py-2 rounded-md bg-background border">
                    {validation.is_valid ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-destructive" />
                    )}
                    <span className="text-sm font-medium">
                      {validation.is_valid ? 'Valid' : 'Has Issues'}
                    </span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{validation.is_valid ? 'Prompt validation passed' : 'Prompt has validation errors or warnings'}</p>
                </TooltipContent>
              </Tooltip>
            )}

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => validatePromptData()}
                  disabled={validating}
                  className="gap-2"
                >
                  {validating ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                  Validate
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Check prompt for syntax errors and validation issues</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Link href={`/prompts/${category}/${id}/preview`}>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Eye className="h-4 w-4" />
                    Preview
                  </Button>
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <p>Preview how this prompt will appear when used</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={savePrompt}
                  disabled={saving || !hasChanges}
                  className="prompt-save-button gap-2"
                >
                  {saving ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  Save Changes
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <div className="space-y-1">
                  <p>Save your changes to the prompt</p>
                  <p className="text-xs text-muted-foreground">Ctrl+S</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Validation Alerts */}
        {validation && !validation.is_valid && validation.errors && validation.errors.length > 0 && (
          <Alert className="border-destructive/50 bg-destructive/10">
            <XCircle className="h-4 w-4 text-destructive" />
            <AlertDescription>
              <div className="font-semibold mb-2 text-destructive">Validation Issues:</div>
              <ul className="list-disc list-inside space-y-1">
                {validation.errors.map((error: string, index: number) => (
                  <li key={index} className="text-sm text-destructive/80">{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {validation && validation.warnings && validation.warnings.length > 0 && (
          <Alert className="border-yellow-500/50 bg-yellow-500/10">
            <AlertCircle className="h-4 w-4 text-yellow-600" />
            <AlertDescription>
              <div className="font-semibold mb-2 text-yellow-700">Warnings:</div>
              <ul className="list-disc list-inside space-y-1">
                {validation.warnings.map((warning: string, index: number) => (
                  <li key={index} className="text-sm text-yellow-600">{warning}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Editor Tabs */}
        <div className="flex items-center justify-between">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
            <TabsList className="grid w-full grid-cols-3 bg-muted/50 p-1 rounded-lg">
              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="content" className="gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                    <FileText className="h-4 w-4" />
                    Content
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    <p>Edit prompt content sections</p>
                    <p className="text-xs text-muted-foreground">Ctrl+1</p>
                  </div>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="metadata" className="gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                    <Settings className="h-4 w-4" />
                    Metadata
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    <p>Configure prompt metadata and settings</p>
                    <p className="text-xs text-muted-foreground">Ctrl+2</p>
                  </div>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="raw" className="gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm">
                    <Code className="h-4 w-4" />
                    Raw Markdown
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    <p>View and edit raw markdown representation</p>
                    <p className="text-xs text-muted-foreground">Ctrl+3</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TabsList>
          </Tabs>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="sm" className="ml-4 gap-2 text-muted-foreground hover:text-foreground">
                <Keyboard className="h-4 w-4" />
                <span className="hidden sm:inline">Shortcuts</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent className="max-w-xs">
              <div className="space-y-2">
                <p className="font-semibold">Keyboard Shortcuts</p>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between gap-4">
                    <span>Save</span>
                    <code className="text-xs bg-muted px-1 rounded">Ctrl+S</code>
                  </div>
                  <div className="flex justify-between gap-4">
                    <span>Content Tab</span>
                    <code className="text-xs bg-muted px-1 rounded">Ctrl+1</code>
                  </div>
                  <div className="flex justify-between gap-4">
                    <span>Metadata Tab</span>
                    <code className="text-xs bg-muted px-1 rounded">Ctrl+2</code>
                  </div>
                  <div className="flex justify-between gap-4">
                    <span>Raw Tab</span>
                    <code className="text-xs bg-muted px-1 rounded">Ctrl+3</code>
                  </div>
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">

          {/* Content Editor */}
          <TabsContent value="content" className="space-y-6">
            {Object.entries(prompt.content).map(([section, content]) => (
              <Card key={section} className="prompt-editor-card">
                <CardHeader className="prompt-editor-header">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg capitalize text-foreground font-semibold flex items-center gap-2">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      {section.replace(/_/g, ' ')}
                    </CardTitle>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit the {section.replace(/_/g, ' ')} section of your prompt</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </CardHeader>
                <CardContent className="pt-6">
                  <Textarea
                    value={content}
                    onChange={(e) => updateContent(section, e.target.value)}
                    className="prompt-editor-textarea min-h-[200px] font-mono text-sm"
                    placeholder={`Enter ${section.replace(/_/g, ' ')} content...`}
                  />
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          {/* Metadata Editor */}
          <TabsContent value="metadata">
            <Card className="prompt-editor-card">
              <CardHeader className="prompt-editor-header">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-foreground font-semibold flex items-center gap-2">
                      <Settings className="h-5 w-5 text-muted-foreground" />
                      Prompt Metadata
                    </CardTitle>
                    <CardDescription className="text-muted-foreground">
                      Configure the metadata and settings for this prompt
                    </CardDescription>
                  </div>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-sm">
                      <p>Metadata helps organize and identify your prompts. Tags can be used for filtering and categorization.</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </CardHeader>
              <CardContent className="space-y-6 pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="title" className="flex items-center gap-2">
                      Title
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-3 w-3 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Display name for this prompt</p>
                        </TooltipContent>
                      </Tooltip>
                    </Label>
                    <Input
                      id="title"
                      value={prompt.metadata.title}
                      onChange={(e) => updateMetadata('title', e.target.value)}
                      className="bg-background border-border focus:border-primary focus:ring-primary/20"
                      placeholder="Enter prompt title..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="version" className="flex items-center gap-2">
                      Version
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-3 w-3 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Semantic version (e.g., 1.0.0)</p>
                        </TooltipContent>
                      </Tooltip>
                    </Label>
                    <Input
                      id="version"
                      value={prompt.metadata.version}
                      onChange={(e) => updateMetadata('version', e.target.value)}
                      className="bg-background border-border focus:border-primary focus:ring-primary/20"
                      placeholder="1.0.0"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description" className="flex items-center gap-2">
                    Description
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-3 w-3 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Brief description of what this prompt does</p>
                      </TooltipContent>
                    </Tooltip>
                  </Label>
                  <Textarea
                    id="description"
                    value={prompt.metadata.description}
                    onChange={(e) => updateMetadata('description', e.target.value)}
                    rows={3}
                    className="bg-background border-border focus:border-primary focus:ring-primary/20 resize-y"
                    placeholder="Describe the purpose and functionality of this prompt..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="author" className="flex items-center gap-2">
                    Author
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-3 w-3 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Person or team who created this prompt</p>
                      </TooltipContent>
                    </Tooltip>
                  </Label>
                  <Input
                    id="author"
                    value={prompt.metadata.author || ''}
                    onChange={(e) => updateMetadata('author', e.target.value)}
                    className="bg-background border-border focus:border-primary focus:ring-primary/20"
                    placeholder="Enter author name..."
                  />
                </div>

                <div className="space-y-3">
                  <Label className="text-foreground font-medium flex items-center gap-2">
                    Tags
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-3 w-3 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Add tags to categorize and filter prompts. Click on a tag to remove it.</p>
                      </TooltipContent>
                    </Tooltip>
                  </Label>
                  <div className="flex flex-wrap gap-2 min-h-[2rem] p-3 bg-muted/20 rounded-md border border-border">
                    {(prompt.metadata.tags || []).length > 0 ? (
                      (prompt.metadata.tags || []).map(tag => (
                        <Tooltip key={tag}>
                          <TooltipTrigger asChild>
                            <Badge
                              variant="secondary"
                              className="prompt-tag"
                              onClick={() => removeTag(tag)}
                            >
                              {tag} ×
                            </Badge>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Click to remove tag</p>
                          </TooltipContent>
                        </Tooltip>
                      ))
                    ) : (
                      <span className="text-muted-foreground text-sm">No tags added yet</span>
                    )}
                  </div>
                  <Input
                    placeholder="Type a tag and press Enter to add..."
                    className="bg-background border-border focus:border-primary focus:ring-primary/20"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        addTag(e.currentTarget.value);
                        e.currentTarget.value = '';
                      }
                    }}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Raw Markdown */}
          <TabsContent value="raw">
            <Card className="prompt-editor-card">
              <CardHeader className="prompt-editor-header">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-foreground font-semibold flex items-center gap-2">
                      <Code className="h-5 w-5 text-muted-foreground" />
                      Raw Markdown
                    </CardTitle>
                    <CardDescription className="text-muted-foreground">
                      View and edit the raw markdown representation of this prompt
                    </CardDescription>
                  </div>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-sm">
                      <p>This shows the complete markdown representation including frontmatter metadata and content sections.</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Info className="h-4 w-4" />
                    <span>Changes made here will update the content and metadata above</span>
                  </div>
                  <Textarea
                    value={generateRawMarkdown()}
                    onChange={(e) => parseRawMarkdown(e.target.value)}
                    className="prompt-editor-textarea min-h-[500px] font-mono text-sm"
                    placeholder="Raw markdown content will appear here..."
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </TooltipProvider>
  );

  function generateRawMarkdown(): string {
    if (!prompt) return '';

    let markdown = `---\n`;
    markdown += `title: "${prompt.metadata.title || prompt.metadata.name || ''}"\n`;
    markdown += `description: "${prompt.metadata.description || ''}"\n`;
    markdown += `version: "${prompt.metadata.version || '1.0.0'}"\n`;
    markdown += `lastModified: "${prompt.metadata.lastModified || new Date().toISOString()}"\n`;
    markdown += `tags: [${(prompt.metadata.tags || []).map((t: string) => `"${t}"`).join(', ')}]\n`;
    if (prompt.metadata.author) {
      markdown += `author: "${prompt.metadata.author}"\n`;
    }
    markdown += `---\n\n`;

    Object.entries(prompt.content).forEach(([section, content]) => {
      markdown += `## ${section.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}\n\n`;
      markdown += `${content}\n\n`;
    });

    return markdown;
  }

  function parseRawMarkdown(markdown: string): void {
    // Simple parser for demonstration
    // In a real implementation, you'd want a more robust markdown parser
    console.log('Parsing raw markdown (not implemented in demo):', markdown);
  }
}
