"use client";

import type { TestExecutionHistoryData } from "@/lib/types";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { ManualTestCasesTable } from "./ManualTestCasesTable";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { callGenerateManualTestCases } from "@/lib/api";

interface TestCasesTabProps {
  historyData: TestExecutionHistoryData;
}

export function TestCasesTab({ historyData }: TestCasesTabProps) {
  const { toast } = useToast();
  const [manualTestCases, setManualTestCases] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addEmptyRow = () => {
    setManualTestCases(prev => [...prev, "Step: . Expected: "]);
  };
  
  const handleGenerateTestCases = async () => {
    if (!historyData.userStory) {
      toast({ 
        title: "No User Story Available", 
        description: "A user story is required to generate manual test cases.",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      let testCases: string[] = [];
      
      if (process.env.NODE_ENV === 'development') {
        // In development mode, use sample data for quicker testing
        testCases = [
          "Step 1: Navigate to login page. Expected: Login form is displayed with username and password fields.",
          "Step 2: Enter invalid credentials. Expected: Error message is shown.",
          "Step 3: Enter valid credentials. Expected: User is redirected to dashboard.",
          "Step 4: Click logout button. Expected: User is logged out and redirected to login page.",
          "Step 5: Try to access protected pages without login. Expected: User is redirected to login page."
        ];
      } else {
        // In production, call the API
        const result = await callGenerateManualTestCases({ 
          userStory: historyData.userStory || ""
        });
        testCases = result.manualTestCases;
      }
      
      setManualTestCases(testCases);
      toast({ title: "Test Cases Generated", description: "Manual test cases were generated successfully." });
    } catch (err) {
      toast({ 
        title: "Generation Failed", 
        description: err instanceof Error ? err.message : "Failed to generate test cases.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // For testing purposes, auto-generate when the component mounts
  useEffect(() => {
    if (historyData.userStory && manualTestCases.length === 0) {
      handleGenerateTestCases();
    }
  }, []);
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="glow-text">Manual Test Cases</CardTitle>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={addEmptyRow}
              >
                + Add Row
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleGenerateTestCases}
                disabled={isLoading || !historyData.userStory}
              >
                <Bot className="mr-2 h-4 w-4" />
                {isLoading ? "Generating..." : "Generate Test Cases"}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {manualTestCases.length > 0 ? (
            <ManualTestCasesTable testCases={manualTestCases} />
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              {historyData.userStory ? 
                "Click the Generate button to create manual test cases based on the user story." : 
                "No user story available. A user story is required to generate manual test cases."}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
