#!/usr/bin/env python3
"""Quick validation of migrated prompts"""

import sys
import os
import json

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from Core.markdown_prompt_loader import <PERSON><PERSON><PERSON>romptLoader
    from Core.prompt_service import PromptService
    
    print("✅ Successfully imported prompt classes")
    
    # Test loader
    loader = MarkdownPromptLoader()
    categories = loader.get_available_categories()
    print(f"📁 Available categories: {categories}")
    
    # Test each category
    for category in categories:
        prompts = loader.get_prompts_in_category(category)
        print(f"   {category}: {len(prompts)} prompts")
        for prompt_name in prompts:
            try:
                prompt_data = loader.load_prompt(category, prompt_name, 'en')
                print(f"      ✅ {prompt_name}: loaded successfully")
            except Exception as e:
                print(f"      ❌ {prompt_name}: error - {e}")
    
    # Test service
    service = PromptService()
    print(f"🔧 PromptService initialized successfully")
    
    print("\n🎉 Migration validation completed!")
    
except Exception as e:
    print(f"❌ Error during validation: {e}")
    import traceback
    traceback.print_exc()
