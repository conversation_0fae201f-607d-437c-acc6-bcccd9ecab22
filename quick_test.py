#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing the prompt system...")
    
    from src.Core.markdown_prompt_loader import MarkdownPromptLoader
    loader = MarkdownPromptLoader("prompts")
    print("✅ Loader created successfully")
    
    available = loader.get_available_prompts()
    print(f"✅ Found {len(available)} categories: {list(available.keys())}")
    
    if "user-story" in available:
        print("✅ user-story category found")
        if "enhance" in available["user-story"]:
            print("✅ enhance prompt found")
            prompt = loader.load_prompt("user-story", "enhance", "en")
            print(f"✅ Prompt loaded: {len(prompt)} characters")
            print("✅ System is working!")
        else:
            print("❌ enhance prompt not found")
    else:
        print("❌ user-story category not found")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
