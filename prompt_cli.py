#!/usr/bin/env python3
"""
Command Line Interface for AgentQA Prompt Management
Provides tools for validating, listing, creating and testing prompts
"""

import sys
import os
import argparse
import json
from pathlib import Path
from typing import Dict, Any

# Add src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.Core.prompt_validator import PromptValidator
from src.Core.markdown_prompt_loader import <PERSON><PERSON><PERSON>rompt<PERSON>oader
from src.Core.prompt_service import PromptService

def list_prompts(prompts_dir: str = "prompts") -> None:
    """List all available prompts organized by category."""
    try:
        loader = MarkdownPromptLoader(prompts_dir)
        available = loader.get_available_prompts()
        
        if not available:
            print("❌ No prompts found")
            return
        
        print("📂 Available Prompts:")
        print("=" * 50)
        
        for category, prompts in available.items():
            print(f"\n🗂️  {category}")
            for prompt_id in prompts:
                print(f"   📝 {prompt_id}")
                
                # Try to get additional info from metadata
                try:
                    metadata = loader.load_metadata(category)
                    for prompt_info in metadata.get("prompts", []):
                        if prompt_info.get("id") == prompt_id:
                            description = prompt_info.get("description", "")
                            languages = ", ".join(prompt_info.get("languages", []))
                            print(f"      Description: {description}")
                            print(f"      Languages: {languages}")
                            break
                except Exception:
                    pass
        
        total_prompts = sum(len(prompts) for prompts in available.values())
        print(f"\n📊 Total: {len(available)} categories, {total_prompts} prompts")
        
    except Exception as e:
        print(f"❌ Error listing prompts: {e}")
        sys.exit(1)

def validate_prompts(prompts_dir: str = "prompts") -> None:
    """Validate all prompts in the system."""
    try:
        validator = PromptValidator(prompts_dir)
        results = validator.validate_all_prompts()
        
        # Print detailed summary
        summary = validator.get_validation_summary(results)
        print(summary)
        
        # Exit with error code if validation failed
        if "error" in results:
            sys.exit(1)
        
        # Check if any category failed validation
        failed_categories = [
            cat for cat, cat_results in results.items() 
            if not cat_results.get("valid", False)
        ]
        
        if failed_categories:
            print(f"\n❌ Validation failed for categories: {', '.join(failed_categories)}")
            sys.exit(1)
        else:
            print("\n✅ All prompts validated successfully!")
            
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        sys.exit(1)

def test_prompt(category: str, prompt_id: str, input_data: str = None, 
                language: str = "en", prompts_dir: str = "prompts") -> None:
    """Test a specific prompt with sample input."""
    try:
        # Initialize prompt service
        service = PromptService(prompts_dir)
        
        # Get prompt variables to know what input is needed
        loader = MarkdownPromptLoader(prompts_dir)
        variables = loader.get_prompt_variables(category, prompt_id)
        
        print(f"🧪 Testing Prompt: {category}:{prompt_id}")
        print(f"📝 Language: {language}")
        print(f"🔧 Required variables: {variables}")
        print("-" * 50)
        
        # Prepare test input
        test_variables = {}
        if input_data:
            # Simple case: if only one variable and input provided, use it
            if len(variables) == 1:
                test_variables[variables[0]] = input_data
            else:
                # Try to parse as JSON if multiple variables
                try:
                    test_variables = json.loads(input_data)
                except json.JSONDecodeError:
                    print(f"❌ For multiple variables, provide JSON input. Variables needed: {variables}")
                    sys.exit(1)
        else:
            # Use default test values
            for var in variables:
                if var == "user_story":
                    test_variables[var] = "Login feature"
                elif var == "test_cases":
                    test_variables[var] = "Test case 1: Valid login\nTest case 2: Invalid credentials"
                elif var == "gherkin_scenario":
                    test_variables[var] = "Given a user on login page\nWhen they enter valid credentials\nThen they should access dashboard"
                else:
                    test_variables[var] = f"sample_{var}_value"
        
        print(f"📥 Input variables:")
        for var, value in test_variables.items():
            print(f"   {var}: {value[:100]}{'...' if len(str(value)) > 100 else ''}")
        print()
        
        # Execute the prompt
        result = service.execute_prompt(category, prompt_id, language, **test_variables)
        
        print("📤 Result:")
        print("-" * 30)
        print(result)
        print("-" * 30)
        print(f"✅ Test completed successfully! ({len(result)} characters)")
        
    except Exception as e:
        print(f"❌ Error testing prompt: {e}")
        sys.exit(1)

def create_prompt(category: str, prompt_id: str, name: str = None, 
                  languages: list = None, prompts_dir: str = "prompts") -> None:
    """Create a new prompt from template."""
    try:
        if languages is None:
            languages = ["en", "es"]
        
        if name is None:
            name = prompt_id.replace("-", " ").replace("_", " ").title()
        
        category_path = Path(prompts_dir) / category
        category_path.mkdir(parents=True, exist_ok=True)
        
        # Create the markdown file
        md_filename = f"{prompt_id}.md"
        md_file_path = category_path / md_filename
        
        if md_file_path.exists():
            print(f"❌ Prompt file already exists: {md_file_path}")
            sys.exit(1)
        
        # Generate markdown content from template
        template = f"""# {name}

## Purpose
[Describe the purpose of this prompt]

## Input Format
- variable_1: Description of the first variable
- variable_2: Description of the second variable

## Output Format
- Describe the expected output format
- Include structure details if applicable

## English Prompt

Your task is to [describe the task in English].

Make sure to include:
1. First requirement
2. Second requirement
3. Third requirement

Input data:
{{variable_1}}

Additional context:
{{variable_2}}

IMPORTANT: Provide only the requested output. Do not include explanatory text or markdown formatting.

## Spanish Prompt

Tu tarea es [describe the task in Spanish].

Asegúrate de incluir:
1. Primer requisito
2. Segundo requisito
3. Tercer requisito

Datos de entrada:
{{variable_1}}

Contexto adicional:
{{variable_2}}

IMPORTANTE: Proporciona únicamente la salida solicitada. No incluyas texto explicativo o formato markdown.

## Variables
- `{{variable_1}}` - Description of the first variable
- `{{variable_2}}` - Description of the second variable

## Examples

### Input
```
variable_1: example input 1
variable_2: example input 2
```

### Output
```
Expected output example
```

## Validation Rules
- Rule 1: Description
- Rule 2: Description

## Version History
- v1.0.0 ({__import__('datetime').datetime.now().strftime('%Y-%m-%d')}): Initial creation
"""
        
        with open(md_file_path, 'w', encoding='utf-8') as f:
            f.write(template)
        
        # Update or create metadata.json
        metadata_file = category_path / "metadata.json"
        if metadata_file.exists():
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
        else:
            metadata = {
                "category": category,
                "version": "1.0.0",
                "description": f"Prompts for {category} operations",
                "prompts": []
            }
        
        # Add new prompt to metadata
        new_prompt = {
            "id": prompt_id,
            "name": name,
            "description": f"[Add description for {name}]",
            "file": md_filename,
            "languages": languages,
            "variables": ["variable_1", "variable_2"],
            "tags": [category],
            "lastModified": __import__('datetime').datetime.now().isoformat() + "Z",
            "author": "AgentQA Team"
        }
        
        # Check if prompt already exists in metadata
        existing_prompt = None
        for i, prompt in enumerate(metadata["prompts"]):
            if prompt["id"] == prompt_id:
                existing_prompt = i
                break
        
        if existing_prompt is not None:
            metadata["prompts"][existing_prompt] = new_prompt
        else:
            metadata["prompts"].append(new_prompt)
        
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Created new prompt: {category}:{prompt_id}")
        print(f"📁 File: {md_file_path}")
        print(f"📋 Metadata updated: {metadata_file}")
        print(f"\n🔧 Next steps:")
        print(f"1. Edit {md_file_path} to customize the prompt")
        print(f"2. Update variables in metadata.json if needed")
        print(f"3. Run 'python prompt_cli.py validate' to check structure")
        print(f"4. Test with 'python prompt_cli.py test --category {category} --id {prompt_id}'")
        
    except Exception as e:
        print(f"❌ Error creating prompt: {e}")
        sys.exit(1)

def info_prompt(category: str, prompt_id: str, prompts_dir: str = "prompts") -> None:
    """Show detailed information about a specific prompt."""
    try:
        loader = MarkdownPromptLoader(prompts_dir)
        
        # Get metadata info
        metadata = loader.load_metadata(category)
        prompt_info = None
        for prompt in metadata.get("prompts", []):
            if prompt["id"] == prompt_id:
                prompt_info = prompt
                break
        
        if not prompt_info:
            print(f"❌ Prompt not found: {category}:{prompt_id}")
            sys.exit(1)
        
        # Get variables from the actual prompt
        variables = loader.get_prompt_variables(category, prompt_id)
        
        print(f"📋 Prompt Information: {category}:{prompt_id}")
        print("=" * 50)
        print(f"Name: {prompt_info.get('name', 'N/A')}")
        print(f"Description: {prompt_info.get('description', 'N/A')}")
        print(f"File: {prompt_info.get('file', 'N/A')}")
        print(f"Languages: {', '.join(prompt_info.get('languages', []))}")
        print(f"Variables: {', '.join(variables) if variables else 'None'}")
        print(f"Tags: {', '.join(prompt_info.get('tags', []))}")
        print(f"Last Modified: {prompt_info.get('lastModified', 'N/A')}")
        print(f"Author: {prompt_info.get('author', 'N/A')}")
        
        # Try to load and show prompt preview
        try:
            prompt_text = loader.load_prompt(category, prompt_id, "en")
            preview = prompt_text[:200] + "..." if len(prompt_text) > 200 else prompt_text
            print(f"\nPreview (English):")
            print("-" * 30)
            print(preview)
            print("-" * 30)
        except Exception as e:
            print(f"\n⚠️  Could not load prompt preview: {e}")
        
    except Exception as e:
        print(f"❌ Error getting prompt info: {e}")
        sys.exit(1)

def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="AgentQA Prompt Management CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python prompt_cli.py list
  python prompt_cli.py validate
  python prompt_cli.py test --category user-story --id enhance
  python prompt_cli.py test --category user-story --id enhance --input "Login feature"
  python prompt_cli.py create --category api-testing --id validate-endpoint
  python prompt_cli.py info --category user-story --id enhance
        """
    )
    
    parser.add_argument('command', choices=['list', 'validate', 'test', 'create', 'info'],
                       help='Command to execute')
    parser.add_argument('--category', '-c', help='Prompt category')
    parser.add_argument('--id', '-i', dest='prompt_id', help='Prompt ID')
    parser.add_argument('--input', '-n', help='Input data for testing')
    parser.add_argument('--language', '-l', default='en', choices=['en', 'es'],
                       help='Language for prompts (default: en)')
    parser.add_argument('--name', help='Name for new prompt (used with create)')
    parser.add_argument('--languages', nargs='+', default=['en', 'es'],
                       help='Languages to support (used with create)')
    parser.add_argument('--prompts-dir', default='prompts',
                       help='Directory containing prompts (default: prompts)')
    
    args = parser.parse_args()
    
    # Validate command-specific arguments
    if args.command in ['test', 'info'] and (not args.category or not args.prompt_id):
        print(f"❌ Command '{args.command}' requires --category and --id arguments")
        sys.exit(1)
    
    if args.command == 'create' and (not args.category or not args.prompt_id):
        print("❌ Command 'create' requires --category and --id arguments")
        sys.exit(1)
    
    # Execute the requested command
    try:
        if args.command == 'list':
            list_prompts(args.prompts_dir)
        
        elif args.command == 'validate':
            validate_prompts(args.prompts_dir)
        
        elif args.command == 'test':
            test_prompt(args.category, args.prompt_id, args.input, args.language, args.prompts_dir)
        
        elif args.command == 'create':
            create_prompt(args.category, args.prompt_id, args.name, args.languages, args.prompts_dir)
        
        elif args.command == 'info':
            info_prompt(args.category, args.prompt_id, args.prompts_dir)
            
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
