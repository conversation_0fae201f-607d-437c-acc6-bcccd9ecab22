

Modelos Soportados - Browser Use
Visión General
Browser Use es compatible con varios modelos de chat de LangChain. A continuación, se explica cómo configurar y utilizar los más populares. La lista completa está disponible en la documentación de LangChain.

Recomendaciones de Modelos
Aún no hemos probado el rendimiento en todos los modelos. Actualmente, obtenemos los mejores resultados utilizando GPT-4o con una precisión del 89% en el Dataset WebVoyager. DeepSeek-V3 es 30 veces más barato que GPT-4o. Gemini-2.0-exp también está ganando popularidad en la comunidad porque actualmente es gratuito. También admitimos modelos locales, como Qwen 2.5, pero ten en cuenta que los modelos pequeños a menudo devuelven una estructura de salida incorrecta, lo que provoca errores de análisis. Creemos que los modelos locales mejorarán significativamente este año.

Todos los modelos requieren sus respectivas claves API. Asegúrate de configurarlas en tus variables de entorno antes de ejecutar el agente.

Modelos Soportados
Todos los modelos de chat de LangChain que admiten la llamada a herramientas (tool-calling) están disponibles. Aquí documentaremos los más populares.

OpenAI
Se recomiendan los modelos GPT-4o de OpenAI para obtener el mejor rendimiento.

from langchain_openai import ChatOpenAI
from browser_use import Agent

# Initialize the model
llm = ChatOpenAI(
    model="gpt-4o",
    temperature=0.0,
)

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)

Variables de entorno requeridas:

OPENAI_API_KEY=

Anthropic
from langchain_anthropic import ChatAnthropic
from browser_use import Agent

# Initialize the model
llm = ChatAnthropic(
    model_name="claude-3-5-sonnet-20240620",
    temperature=0.0,
    timeout=100, # Increase for complex tasks
)

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)

Y añade la variable:

ANTHROPIC_API_KEY=

Azure OpenAI
from langchain_openai import AzureChatOpenAI
from browser_use import Agent
from pydantic import SecretStr
import os

# Initialize the model
llm = AzureChatOpenAI(
    model="gpt-4o",
    api_version='2024-10-21',
    azure_endpoint=os.getenv('AZURE_OPENAI_ENDPOINT', ''),
    api_key=SecretStr(os.getenv('AZURE_OPENAI_KEY', '')),
)

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)

Variables de entorno requeridas:

AZURE_OPENAI_ENDPOINT=[https://your-endpoint.openai.azure.com/](https://your-endpoint.openai.azure.com/)
AZURE_OPENAI_KEY=

Gemini
[!IMPORTANT]
GEMINI_API_KEY era el antiguo nombre de la variable de entorno, debería llamarse GOOGLE_API_KEY a partir del 05-2025.

from langchain_google_genai import ChatGoogleGenerativeAI
from browser_use import Agent
from dotenv import load_dotenv

# Read GOOGLE_API_KEY into env
load_dotenv()

# Initialize the model
llm = ChatGoogleGenerativeAI(model='gemini-2.0-flash-exp')

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)

Variables de entorno requeridas:

GOOGLE_API_KEY=

DeepSeek-V3
A la comunidad le gusta DeepSeek-V3 por su bajo precio, sin límites de tasa, naturaleza de código abierto y buen rendimiento. El ejemplo está disponible aquí.

from langchain_deepseek import ChatDeepSeek
from browser_use import Agent
from pydantic import SecretStr
from dotenv import load_dotenv
import os

load_dotenv()
api_key = os.getenv("DEEPSEEK_API_KEY")

# Initialize the model
llm=ChatDeepSeek(base_url='[https://api.deepseek.com/v1](https://api.deepseek.com/v1)', model='deepseek-chat', api_key=SecretStr(api_key))

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm,
    use_vision=False
)

Variables de entorno requeridas:

DEEPSEEK_API_KEY=

DeepSeek-R1
Admitimos DeepSeek-R1. Aún no está completamente probado, se agregarán más y más funcionalidades, como por ejemplo, la salida de su contenido de razonamiento. El ejemplo está disponible aquí. No admite visión. El modelo es de código abierto, por lo que también podrías usarlo con Ollama, pero no lo hemos probado.

from langchain_deepseek import ChatDeepSeek
from browser_use import Agent
from pydantic import SecretStr
from dotenv import load_dotenv
import os

load_dotenv()
api_key = os.getenv("DEEPSEEK_API_KEY")

# Initialize the model
llm=ChatDeepSeek(base_url='[https://api.deepseek.com/v1](https://api.deepseek.com/v1)', model='deepseek-reasoner', api_key=SecretStr(api_key))

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm,
    use_vision=False
)

Variables de entorno requeridas:

DEEPSEEK_API_KEY=

Ollama
Muchos usuarios pidieron modelos locales. Aquí están.

Descarga Ollama desde aquí.

Ejecuta ollama pull model_name. Elige un modelo que admita la llamada a herramientas desde aquí.

Ejecuta ollama start.

from langchain_ollama import ChatOllama
from browser_use import Agent
from pydantic import SecretStr

# Initialize the model
llm=ChatOllama(model="qwen2.5", num_ctx=32000)

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm
)

Variables de entorno requeridas: ¡Ninguna!

Novita AI
Novita AI es un proveedor de API LLM que ofrece una amplia gama de modelos.
Nota: elige un modelo que admita la llamada a funciones.

from langchain_openai import ChatOpenAI
from browser_use import Agent
from pydantic import SecretStr
from dotenv import load_dotenv
import os

load_dotenv()
api_key = os.getenv("NOVITA_API_KEY")

# Initialize the model
llm = ChatOpenAI(base_url='[https://api.novita.ai/v3/openai](https://api.novita.ai/v3/openai)', model='deepseek/deepseek-v3-0324', api_key=SecretStr(api_key))

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm,
    use_vision=False
)

Variables de entorno requeridas:

NOVITA_API_KEY=

X AI
X AI es un proveedor de API LLM que ofrece una amplia gama de modelos.
Nota: elige un modelo que admita la llamada a funciones.

from langchain_openai import ChatOpenAI
from browser_use import Agent
from pydantic import SecretStr
from dotenv import load_dotenv
import os

load_dotenv()
api_key = os.getenv("GROK_API_KEY")

# Initialize the model
llm = ChatOpenAI(
    base_url='[https://api.x.ai/v1](https://api.x.ai/v1)',
    model='grok-3-beta',
    api_key=SecretStr(api_key)
)

# Create agent with the model
agent = Agent(
    task="Your task here",
    llm=llm,
    use_vision=False
)

Variables de entorno requeridas:

GROK_API_KEY=

Próximamente (Estamos trabajando en ello)
Groq

Github

Modelos Afinados (Fine-tuned models)