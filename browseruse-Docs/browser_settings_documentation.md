# Configuración del Navegador - Browser Use

Configura el comportamiento del navegador y los ajustes de contexto. Browser Use utiliza Playwright (o Patchright) + CDP para gestionar su conexión con un navegador real.

```python
from browser_use import BrowserProfile, BrowserSession

# BrowserSession(**params) es el objeto de Browser Use que rastrea una conexión a un navegador en ejecución.
# Contiene:
# - todos los kwargs estándar de playwright
# - algunas opciones de configuración de navegador adicionales que proporcionamos además de playwright (por ejemplo, allowed_domains, window_position, profile_directory, deterministic_rendering, etc.)
# - algunos kwargs utilizados para configurar características específicas de Browser Use relacionadas con el navegador (por ejemplo, highlight_elements, disable_security, cookies_file)

# Browser: objeto estándar de playwright <PERSON><PERSON><PERSON>, se refiere a un proceso de navegador en ejecución (remoto o local)
# BrowserContext: objeto estándar de playwright B<PERSON><PERSON><PERSON>ontext, se refiere a una ventana en un navegador activo (incógnito o con un perfil)
# Page: objeto estándar de playwright Page, el manejador para una pestaña en el navegador

Las nuevas clases BrowserSession y BrowserProfile ahora aceptan todos los mismos argumentos que la API estándar launch_persistent_context de Playwright, dándote control total sobre la configuración del navegador.

Configuración del Navegador
from browser_use import Agent, BrowserSession, BrowserProfile

# Es fácil conectarse o lanzar un nuevo navegador con una BrowserSession, se admiten muchos métodos:
browser_session = BrowserSession(
    cdp_url='http://localhost:9222', # o wss_url='ws://...',
    # keep_alive=True,
    # browser_pid=12445,
    # page=playwright_page | browser_context | browser | playwright,
    # executable_path='...',
    # user_data_dir='...',
    # headless=True,
    browser_profile=BrowserProfile(..., color_scheme='dark'),
    # color_scheme='light',  # <- tendrá precedencia sobre ^
    # ... otras anulaciones de configuración pueden ir aquí
)

# opcionalmente puedes iniciar una sesión y usarla fuera de un Agent, de lo contrario Agent la iniciará automáticamente
await browser_session.start()
page = await browser_session.browser_context.new_page()
await page.goto('[https://example.com/load-before-agent-starts](https://example.com/load-before-agent-starts)')

agent = Agent(
    task='your task here',
    browser_session=browser_session,  # proporciona una browser_session al agente
    # page=page,  # o como atajo, proporciona una página de playwright directamente
)
```BrowserProfile` es una colección estática y plana de configuraciones. Se puede pasar a una `BrowserSession` para iniciar una sesión utilizando esa configuración.

```python
browser_profile = BrowserProfile(
    headless=True,
    is_mobile=True,
    **playwright.devices['iPhone 13'],
    user_data_dir='~/Desktop/mobile_test_profile',
    allowed_domains=['https://*.example.com'],
)

# crea una nueva sesión usando el perfil
browser_session = BrowserSession(
    browser_profile=browser_profile,
    headless=False,  # <- los kwargs adicionales pasados a la sesión anularán los valores del perfil
)
```BrowserSession` y `BrowserProfile` aceptan la misma larga lista de kwargs de configuración principal, la mayoría son argumentos estándar de Playwright que toma `playwright.BrowserType.launch_persistent_context()`. También proporcionamos algunas opciones de utilidad adicionales para controlar características específicas de Browser-Use y facilitar la configuración, por ejemplo, `allowed_domains`, `keep_alive`, `highlight_elements`, y más.

## Parámetros de `BrowserSession`

### Parámetros de Conexión del Navegador Remoto

#### `wss_url`

wss_url: str | None = None

URL WSS del servidor de navegador Playwright de node.js al que conectarse.

#### `cdp_url`

cdp_url: str | None = None

URL CDP del navegador al que conectarse (por ejemplo, `http://localhost:9222`).

#### `browser_pid`

browser_pid: int | None = None

PID de un proceso de navegador basado en Chromium en ejecución al que conectarse en localhost.

Para tareas de web scraping en sitios que restringen el acceso automatizado, recomendamos usar nuestra nube o un proveedor de navegador externo para una mejor fiabilidad. Consulta la guía [Conectar a tu Navegador](https://docs.browser-use.com/customize/connect-to-browser) para obtener instrucciones detalladas de conexión.

### Estado / Parámetros en Tiempo de Ejecución

#### `browser_profile`

browser_profile: BrowserProfile = BrowserProfile()

Instancia de `BrowserProfile` que contiene la configuración a usar para la `BrowserSession`.

#### `playwright`

playwright: Playwright | None = None

Objeto cliente API opcional de Playwright o Patchright a usar (resultado de `(await async_playwright().start())` o `(await async_patchright().start())`).

#### `browser`

browser: Browser | None = None

Objeto `Browser` de Playwright a usar (opcional).

#### `browser_context`

browser_context: BrowserContext | None = None

Objeto `BrowserContext` de Playwright a usar (opcional).

#### `initialized`

initialized: bool = False

Marcar `BrowserSession` como ya inicializada, omite el lanzamiento/conexión (no recomendado).

#### `page` (alias `agent_current_page`)

page: Page | None = None

Página en primer plano en la que el agente está enfocado, también se puede pasar como `page=...` como atajo.

#### `human_current_page`

human_current_page: Page | None = None

Página en primer plano en la que el humano está enfocado.

### Parámetros de Browser-Use

Estos parámetros controlan características específicas de Browser-Use y están fuera del conjunto de parámetros estándar de Playwright.

#### `keep_alive`

keep_alive: bool | None = None

Mantiene el navegador vivo después de que el agente haya terminado de ejecutarse. Útil para ejecutar múltiples tareas con la misma instancia de navegador.

#### `allowed_domains`

allowed_domains: list[str] | None = None

Lista de dominios permitidos para la navegación. Si es `None`, todos los dominios están permitidos.
Ejemplo: `['google.com', '*.wikipedia.org']` - Aquí el agente solo podrá acceder a `google.com` exactamente y a `wikipedia.org` + `*.wikipedia.org`.
Se admiten patrones globales:
* `['example.com']` ✅ coincidirá solo con `https://example.com/*` exactamente, los subdominios no estarán permitidos. Siempre es más seguro listar explícitamente todos los dominios a los que quieres dar acceso con esquemas.
* `['*.example.com']` ⚠️ PRECAUCIÓN: esto coincidirá con `https://example.com` y todos sus subdominios. ¡Asegúrate de que todos los subdominios sean seguros para el agente! `abc.example.com`, `def.example.com`, ..., `useruploads.example.com`, `admin.example.com`.

#### `disable_security`

disable_security: bool = False

Desactiva completamente todas las funciones básicas de seguridad del navegador. Permite interactuar a través de los límites de iFrames de sitios cruzados, pero es MUY INSEGURO, no visites URLs no confiables ni uses cookies.

#### `deterministic_rendering`

deterministic_rendering: bool = False

Habilita las banderas de renderizado determinista para capturas de pantalla consistentes.

#### `highlight_elements`

highlight_elements: bool = True

Resalta los elementos interactivos en la pantalla con cuadros delimitadores de colores.

#### `viewport_expansion`

viewport_expansion: int = 500

Expansión del viewport en píxeles. Con esto puedes controlar qué parte de la página se incluye en el contexto del LLM:
* `-1`: Se incluirán todos los elementos de toda la página, independientemente de la visibilidad (mayor uso de tokens pero más completo).
* `0`: Solo se incluirán los elementos que están actualmente visibles en el viewport.
* `500` (predeterminado): Se incluirán los elementos en el viewport más 500 píxeles adicionales en cada dirección, proporcionando un equilibrio entre contexto y uso de tokens.

#### `include_dynamic_attributes`

include_dynamic_attributes: bool = True

Incluye atributos dinámicos en los selectores para una mejor selección de elementos.

#### `minimum_wait_page_load_time`

minimum_wait_page_load_time: float = 0.25

Tiempo mínimo a esperar antes de capturar el estado de la página para la entrada del LLM.

#### `wait_for_network_idle_page_load_time`

wait_for_network_idle_page_load_time: float = 0.5

Tiempo a esperar para que cese la actividad de la red. Aumenta a 3-5s para sitios web más lentos. Esto rastrea la carga de contenido esencial, no elementos dinámicos como videos.

#### `maximum_wait_page_load_time`

maximum_wait_page_load_time: float = 5.0

Tiempo máximo a esperar para la carga de la página antes de continuar.

#### `wait_between_actions`

wait_between_actions: float = 0.5

Tiempo a esperar entre acciones del agente.

#### `cookies_file`

cookies_file: str | None = None

Archivo para guardar las cookies.

#### `downloads_dir`

downloads_dir: Path | str = '~/.config/browseruse/downloads'

Directorio para descargas.

#### `save_downloads_path`

save_downloads_path: str | None = None

Directorio para guardar descargas (alternativa a `downloads_dir`).

#### `profile_directory`

profile_directory: str = 'Default'

Nombre del directorio del perfil de Chrome (por ejemplo, 'Profile 1', 'Profile 2').

#### `window_position`

window_position: dict | None = {"width": 0, "height": 0}

Posición de la ventana desde la esquina superior izquierda.

## Parámetros de Playwright

[https://playwright.dev/python/docs/api/class-browsertype#browser-type-launch-persistent-context](https://playwright.dev/python/docs/api/class-browsertype#browser-type-launch-persistent-context)

Todos los parámetros a continuación son parámetros estándar de Playwright y se pueden pasar tanto a `BrowserSession` como a `BrowserProfile`. Están definidos en `browser_use/browser/profile.py`.

### Ajustes de Lanzamiento

#### `headless`

headless: bool | None = None

Ejecuta el navegador sin una interfaz de usuario visible. Si es `None`, se autodecta según la disponibilidad de la pantalla.

#### `channel`

channel: BrowserChannel = 'chromium'

Canal del navegador: `'chromium'`, `'chrome'`, `'chrome-beta'`, `'chrome-dev'`, `'chrome-canary'`, `'msedge'`, `'msedge-beta'`, `'msedge-dev'`, `'msedge-canary'`.

#### `executable_path`

executable_path: str | Path | None = None

Ruta al ejecutable del navegador para instalaciones personalizadas.

#### `user_data_dir`

user_data_dir: str | Path | None = '~/.config/browseruse/profiles/default'

Directorio para los datos del perfil del navegador. Establécelo en `None` para usar un perfil efímero (modo incógnito).

#### `args`

args: list[str] = []

Argumentos adicionales de línea de comandos para pasar al navegador.

#### `ignore_default_args`

ignore_default_args: list[str] | bool = ['--enable-automation', '--disable-extensions']

Lista de argumentos CLI predeterminados para evitar que Playwright los aplique.

#### `env`

env: dict[str, str] = {}

Variables de entorno a establecer al lanzar el navegador.

#### `chromium_sandbox`

chromium_sandbox: bool = not IN_DOCKER # (IN_DOCKER es una variable que indica si se está ejecutando en Docker)

Si se habilita el sandboxing de Chromium (recomendado a menos que esté dentro de Docker).

#### `devtools`

devtools: bool = False

Si se abre automáticamente el panel de DevTools (solo funciona cuando `headless=False`).

#### `slow_mo`

slow_mo: float = 0

Ralentiza las acciones en esta cantidad de milisegundos.

#### `timeout`

timeout: float = 30000

Tiempo de espera predeterminado en milisegundos para conectarse a un navegador remoto.

#### `accept_downloads`

accept_downloads: bool = True

Si se aceptan automáticamente todas las descargas.

#### `proxy`

proxy: dict | None = None

Configuración del proxy.
