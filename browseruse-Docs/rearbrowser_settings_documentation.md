Conectarse a tu Navegador - Browser Use
Conéctate a un navegador remoto o lanza un nuevo navegador local.

Visión General
A partir de Chrome v136, ya no se admite la conducción de Chrome a través de CDP con el perfil predeterminado. Browser-Use ha pasado a crear un nuevo perfil dedicado para los agentes en: ~/.config/browseruse/profiles/default.
Puedes abrir este perfil e iniciar sesión en todo lo que necesites que tu agente tenga acceso, y persistirá con el tiempo.

Métodos de Conexión
Método A: Lanzar un Nuevo Navegador Local (Predeterminado)
Lanza un navegador local utilizando el predeterminado incorporado (Playwright Chromium) o una executable_path proporcionada:

from browser_use import Agent, BrowserSession

# Si no se proporciona executable_path, utiliza el Chromium incorporado de Playwright/Patchright
browser_session = BrowserSession(
    # Ruta a un ejecutable específico basado en Chromium (opcional)
    executable_path='/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',  # macOS
    # Para Windows: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe'
    # Para Linux: '/usr/bin/google-chrome'
    # Usar un directorio de datos específico en el disco (opcional)
    user_data_dir='~/.config/browseruse/profiles/default',
    # ... cualquier otra configuración de BrowserProfile o playwright launch_persistent_context...
    # headless=False,
)

agent = Agent(
    task="Your task here",
    llm=llm, # Asumiendo que 'llm' está definido previamente
    browser_session=browser_session,
)

Método B: Conectar Usando Objetos Playwright Existentes
Pasa objetos Page, BrowserContext y/o Browser de Playwright existentes a Agent(...) y/o BrowserSession(...):

from browser_use import Agent, BrowserSession # Asumiendo que BrowserSession también se importa si se usa
from playwright.async_api import async_playwright
# o from patchright.async_api import async_patchright

async def main(): # Envuelto en una función async para 'await'
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch()
        context = await browser.new_context()
        page = await context.new_page()

        browser_session = BrowserSession(
            page=page,
            # o browser_context=context, o browser=browser, o playwright=playwright
        )

        # Se puede pasar cualquiera de: page, browser_context, browser, playwright
        agent = Agent(
            task="Your task here",
            llm=llm, # Asumiendo que 'llm' está definido previamente
            page=page,
            # o browser_session=browser_session, o browser_context=context, o browser=browser
        )
        # ... aquí iría la lógica del agente ...
        # await agent.run() # Ejemplo
        await browser.close()

# import asyncio
# asyncio.run(main()) # Para ejecutar la función async

Método C: Conectar a Navegador Local Usando PID del Navegador
Conéctate a un navegador con open --remote-debugging-port:

from browser_use import Agent, BrowserSession

# Primero, inicia Chrome con depuración remota:
# /Applications/Google Chrome.app/Contents/MacOS/Google Chrome --remote-debugging-port=9242
# Luego conéctate usando el ID del proceso
browser_session = BrowserSession(browser_pid=12345)  # Reemplaza con el PID real de Chrome

agent = Agent(
    task="Your task here",
    llm=llm, # Asumiendo que 'llm' está definido previamente
    browser_session=browser_session,
)

Método D: Conectar a Servidor de Navegador Node.js Remoto de Playwright mediante URL WSS
Conéctate a proveedores de servidores Node.js de Playwright:

from browser_use import Agent, BrowserSession

# Conectarse a un servidor de playwright
browser_session = BrowserSession(wss_url="wss://[your-playwright-server.com/ws](https://your-playwright-server.com/ws)")

agent = Agent(
    task="Your task here",
    llm=llm, # Asumiendo que 'llm' está definido previamente
    browser_session=browser_session,
)

Método E: Conectar a Navegador Remoto mediante URL CDP
Conéctate a cualquier navegador remoto basado en Chromium:

from browser_use import Agent, BrowserSession

# Conectarse a Chrome mediante CDP
browser_session = BrowserSession(cdp_url="http://localhost:9222")

agent = Agent(
    task="Your task here",
    llm=llm, # Asumiendo que 'llm' está definido previamente
    browser_session=browser_session,
)

Consideraciones de Seguridad
Cuando se utiliza cualquier perfil de navegador, el agente tendrá acceso a:

Todas sus sesiones iniciadas y cookies

Contraseñas guardadas (si el autocompletado está habilitado)

Historial de navegación y marcadores

Extensiones y sus datos

¡Revisa siempre la tarea que le estás dando al agente y asegúrate de que se alinee con tus requisitos de seguridad!
Usa Agent(sensitive_data={'https://auth.example.com': {x_key: value}}) para cualquier secreto, y restringe el navegador con BrowserSession(allowed_domains=['https://*.example.com']).

Mejores Prácticas
Usa perfiles aislados: Crea perfiles de Chrome separados para diferentes agentes para limitar el alcance del riesgo:

from browser_use import BrowserProfile # Asegúrate de importar BrowserProfile

profile = BrowserProfile(
    user_data_dir='~/.config/browseruse/profiles/agent_profile_name', # Cambia 'default' por un nombre específico
    # profile_directory='AgentProfileName' # Opcional, si usas directorios de perfil nombrados dentro de user_data_dir
)

Limita el acceso a dominios: Restringe los sitios que el agente puede visitar:

browser_session = BrowserSession(
    allowed_domains=['example.com', 'http*://*.github.com'],
    # ... otras configuraciones
)

Habilita keep_alive para múltiples tareas: Mantén el navegador abierto entre ejecuciones del agente:

browser_session = BrowserSession(
    keep_alive=True,
    # ... otras configuraciones
)

Solución de Problemas
Chrome No se Conecta
Si tienes problemas para conectarte:

Cierra todas las instancias de Chrome antes de intentar lanzar con un perfil personalizado.

Verifica si Chrome se está ejecutando con el puerto de depuración:

ps aux | grep chrome | grep remote-debugging-port

Verifica que la ruta del ejecutable sea correcta para tu sistema.

Verifica los permisos del perfil: asegúrate de que tu usuario tenga acceso de lectura/escritura.

Problemas de Bloqueo de Perfil
Si obtienes un error de "el perfil ya está en uso":

Cierra todas las instancias de Chrome.

El perfil se desbloqueará automáticamente cuando BrowserSession se inicie.

Alternativamente, elimina manualmente el archivo SingletonLock en el directorio del perfil.

Para más opciones de configuración, consulta la documentación de Configuración del Navegador.r