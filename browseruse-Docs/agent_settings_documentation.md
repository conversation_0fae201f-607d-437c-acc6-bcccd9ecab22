# Browser Use Agent Settings Documentation

This document summarizes the configuration options for the `Agent` class in the Browser Use library.

## Basic Settings

These are the essential parameters for initializing an agent.

*   `task` (required): The instruction for the agent to execute.
*   `llm` (required): A LangChain chat model instance.

## Agent Behavior

Controls how the agent operates.

*   `controller`: Registry of functions the agent can call. Defaults to base Controller. (See [Custom Functions](https://docs.browser-use.com/customize/custom-functions) for details).
*   `use_vision` (default: `True`): Enable/disable vision capabilities. When enabled, the model processes visual information from web pages. Disable to reduce costs or use models without vision support.
*   `save_conversation_path`: Path to save the complete conversation history. Useful for debugging.
*   `override_system_message`: Completely replace the default system prompt with a custom one.
*   `extend_system_message`: Add additional instructions to the default system prompt.

## (Reuse) Browser Configuration

Configure how the agent interacts with the browser. (See [Browser Settings](https://docs.browser-use.com/customize/browser-settings) for more `Browser` options).

*   **Reuse Existing Browser:**
    *   `browser`: A Browser Use `Browser` instance. When provided, the agent will reuse this browser instance and automatically create new contexts for each `run()`. The `Browser` will not be closed automatically in this scenario.
*   **Reuse Existing Browser Context:**
    *   `browser_context`: A Playwright browser context. Useful for maintaining persistent sessions. (See [Persistent Browser](https://docs.browser-use.com/customize/persistent-browser) for more details). The browser will be automatically created and closed on `run()` completion if not reusing a context.

## Running the Agent

The agent is executed using the async `run()` method.

*   `max_steps` (default: `100`): Maximum number of steps the agent can take during execution. This prevents infinite loops and helps control execution time.

## Agent History

The `run()` method returns an `AgentHistoryList` object containing the complete execution history.

*   **`AgentHistoryList` Methods & Properties:**
    *   `urls()`: List of visited URLs.
    *   `screenshots()`: List of screenshot paths.
    *   `action_names()`: Names of executed actions.
    *   `extracted_content()`: Content extracted during execution.
    *   `errors()`: Any errors that occurred.
    *   `model_actions()`: All actions with their parameters.
    *   `final_result()`: Get the final extracted content.
    *   `is_done()`: Check if the agent completed successfully.
    *   `has_errors()`: Check if any errors occurred.
    *   `model_thoughts()`: Get the agent’s reasoning process.
    *   `action_results()`: Get results of all actions.
    *   (For a complete list, refer to the [AgentHistoryList source code](https://github.com/browser-use/browser-use/blob/main/browser_use/agent/views.py#L111)).

## Run initial actions without LLM

Specify initial actions to run before the LLM takes over. Actions are provided as a list of dictionaries.

*   `initial_actions`: A list of action dictionaries (e.g., `[{'open_tab': {'url': 'https://www.google.com'}}, {'scroll_down': {'amount': 1000}}]`). (Find all actions in the [Controller source code](https://github.com/browser-use/browser-use/blob/main/browser_use/controller/service.py)).

## Run with message context

Provide additional information to help the LLM understand the task better.

*   `message_context`: A string containing additional information about the task.

## Run with planner model

Configure the agent to use a separate planner model for high-level task planning.

*   **Planner Parameters:**
    *   `planner_llm`: A LangChain chat model instance used for high-level task planning. Can be a smaller/cheaper model than the main LLM.
    *   `use_vision_for_planner` (default: `True`): Enable/disable vision capabilities for the planner model.
    *   `planner_interval` (default: `1`): Number of steps between planning phases.
*   **Optional Parameters (can also be used without a planner):**
    *   `message_context`: Additional information about the task.
    *   `initial_actions`: List of initial actions.
    *   `max_actions_per_step` (default: `10`): Maximum number of actions to run in a step.
    *   `max_failures` (default: `3`): Maximum number of failures before giving up.
    *   `retry_delay` (default: `10`): Time to wait between retries in seconds when rate limited.
    *   `generate_gif` (default: `False`): Enable/disable GIF generation. Set to `True` or a string path to save the GIF.

## Memory Management

Includes a procedural memory system using [Mem0](https://mem0.ai/) to summarize conversation history.

*   **Memory Parameters:**
    *   `enable_memory` (default: `True`): Enable/disable the procedural memory system.
    *   `memory_config`: A `MemoryConfig` Pydantic model instance (required). Dictionary format is not supported.
*   **`MemoryConfig` Settings:**
    *   **Memory Settings:**
        *   `agent_id` (default: `"browser_use_agent"`): Unique identifier for the agent.
        *   `memory_interval` (default: `10`): Number of steps between memory summarization.
    *   **Embedder Settings:**
        *   `embedder_provider`: Provider for embeddings (`'openai'`, `'gemini'`, `'ollama'`, or `'huggingface'`).
        *   `embedder_model`: Model name for the embedder.
        *   `embedder_dims`: Dimensions for the embeddings.
        *   (Defaults are set based on the LLM: OpenAI uses `text-embedding-3-small`, Gemini uses `models/text-embedding-004`, Ollama uses `nomic-embed-text`, and Hugging Face `all-MiniLM-L6-v2` is the general default).
    *   **Vector Store Settings:**
        *   `vector_store_provider` (currently only `'faiss'` is supported): Provider for vector storage.
        *   `vector_store_base_path`: Path for storing vector data (e.g. `/tmp/mem0`).
*   **How Memory Works:**
    *   Periodically (every `memory_interval` steps), the agent reviews recent interactions.
    *   It creates a procedural memory summary using the same LLM as the agent.
    *   Original messages are replaced with the summary to reduce token usage.
*   **Disabling Memory:**
    *   Set `enable_memory=False`. Useful for debugging or short tasks, but can lead to context window overflow for longer tasks.
