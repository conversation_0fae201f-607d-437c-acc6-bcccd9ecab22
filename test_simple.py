#!/usr/bin/env python3
"""
Simple test to verify the prompt system is working
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that we can import all required modules."""
    print("🧪 Testing imports...")
    
    try:
        from src.Core.markdown_prompt_loader import MarkdownPromptLoader
        print("✅ MarkdownPromptLoader imported successfully")
        
        from src.Core.prompt_markdown_parser import PromptMarkdownParser
        print("✅ PromptMarkdownParser imported successfully")
        
        from src.Core.prompt_service import PromptService
        print("✅ PromptService imported successfully")
        
        from src.Core.prompt_validator import PromptValidator
        print("✅ PromptValidator imported successfully")
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality."""
    print("\n🧪 Testing basic functionality...")
    
    try:
        from src.Core.markdown_prompt_loader import Markdown<PERSON>romptLoader
        
        # Test loader initialization
        loader = MarkdownPromptLoader("prompts")
        print("✅ Loader initialized")
        
        # Test getting available prompts
        available = loader.get_available_prompts()
        print(f"✅ Found {len(available)} categories")
        
        for category, prompts in available.items():
            print(f"   📂 {category}: {len(prompts)} prompts")
            for prompt_id in prompts:
                print(f"      📝 {prompt_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Functionality test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation():
    """Test validation functionality."""
    print("\n🧪 Testing validation...")
    
    try:
        from src.Core.prompt_validator import PromptValidator
        
        validator = PromptValidator("prompts")
        results = validator.validate_all_prompts()
        
        if "error" in results:
            print(f"❌ Validation error: {results['error']}")
            return False
        
        print(f"✅ Validated {len(results)} categories")
        
        for category, cat_results in results.items():
            status = "✅" if cat_results.get("valid", False) else "❌"
            print(f"   {status} {category}")
            
            if cat_results.get("errors"):
                for error in cat_results["errors"][:3]:  # Show first 3 errors
                    print(f"      ❌ {error}")
            
            if cat_results.get("warnings"):
                for warning in cat_results["warnings"][:3]:  # Show first 3 warnings
                    print(f"      ⚠️  {warning}")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 AgentQA Prompt System Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Basic Functionality", test_basic_functionality),
        ("Validation", test_validation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The prompt system is working correctly.")
        return True
    else:
        print("💥 Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
