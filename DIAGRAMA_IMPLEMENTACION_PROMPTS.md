# 🏗️ Diagrama de Implementación - Sistema de Prompts Basado en Markdown

## 📊 Arquitectura General del Sistema

```mermaid
graph TB
    subgraph "🌐 Frontend (Streamlit + Next.js)"
        A[QA Assistant UI] 
        B[Project Manager UI]
        C[Test History UI]
    end
    
    subgraph "🎯 Capa de Servicios"
        D[PromptService]
        E[ChecklistManager]
        F[TestExecutor]
    end
    
    subgraph "📂 Sistema de Prompts (NUEVO)"
        G[MarkdownPromptLoader]
        H[PromptMarkdownParser]
        I[PromptValidator]
        J[PromptCache]
    end
    
    subgraph "📁 Prompts Markdown"
        K[user-story/enhance.md]
        L[test-cases/manual-generation.md]
        M[code-generation/cypress.md]
        N[browser-automation/task-generation.md]
    end
    
    subgraph "🤖 LLM Integration"
        O[ChatGoogleGenerativeAI]
        P[Browser Agent]
    end
    
    A --> D
    B --> D
    C --> D
    D --> G
    G --> H
    G --> I
    G --> J
    H --> K
    H --> L
    H --> M
    H --> N
    D --> O
    D --> P
    
    style G fill:#e1f5fe
    style H fill:#e8f5e8
    style I fill:#fff3e0
    style J fill:#f3e5f5
```

## 🗂️ Estructura de Directorios Implementada

```
AgentQA/
├── src/Prompts/                    # 🔴 ACTUAL (a migrar)
│   ├── agno_prompts_clean.py       # Legacy - contiene prompts embebidos
│   ├── agno_prompts.py             # Legacy - funciones con prompts
│   ├── browser_prompts.py          # Funciones dinámicas
│   ├── code_gen_prompts.py         # Prompts estructurados
│   ├── gherkin_prompts.py          # Prompts estructurados
│   ├── prompt_manager.py           # Manager actual básico
│   └── ...
│
└── prompts/                        # 🟢 NUEVO SISTEMA
    ├── user-story/
    │   ├── enhance.md              # Mejora de historias de usuario
    │   └── metadata.json           # Metadatos del módulo
    ├── test-cases/
    │   ├── manual-generation.md    # Generación casos manuales
    │   ├── gherkin-conversion.md   # Conversión a Gherkin
    │   └── metadata.json
    ├── code-generation/
    │   ├── selenium-pytest.md      # Código Selenium + PyTest
    │   ├── playwright-python.md    # Código Playwright
    │   ├── cypress-javascript.md   # Código Cypress
    │   ├── robot-framework.md      # Código Robot Framework
    │   ├── java-selenium.md        # Código Java + Selenium
    │   └── metadata.json
    ├── test-analysis/
    │   ├── results-summary.md      # Resumen de resultados
    │   └── metadata.json
    ├── browser-automation/
    │   ├── task-generation.md      # Generación de tareas browser
    │   └── metadata.json
    └── shared/
        ├── templates/              # Templates comunes
        └── variables.json          # Variables globales
```

## ⚙️ Flujo de Implementación por Fases

```mermaid
gantt
    title Plan de Implementación Sistema Prompts
    dateFormat  YYYY-MM-DD
    section Fase 1: Infraestructura
    Crear estructura directorios    :done, infra1, 2025-05-30, 1d
    MarkdownPromptLoader           :active, infra2, 2025-05-31, 2d
    PromptMarkdownParser           :infra3, after infra2, 2d
    PromptValidator                :infra4, after infra3, 1d
    Tests unitarios                :infra5, after infra4, 1d
    
    section Fase 2: Migración Core
    user_story_prompts.py          :mig1, after infra5, 1d
    test_case + gherkin_prompts    :mig2, after mig1, 2d
    code_gen_prompts.py            :mig3, after mig2, 2d
    test_summarization_prompts     :mig4, after mig3, 1d
    
    section Fase 3: Legacy
    Extraer agno_prompts           :leg1, after mig4, 2d
    Migrar browser_prompts         :leg2, after leg1, 1d
    CLI de gestión                 :leg3, after leg2, 1d
    
    section Fase 4: Integración
    Actualizar app.py              :int1, after leg3, 1d
    Tests integración E2E          :int2, after int1, 1d
    Documentación final            :int3, after int2, 1d
```

## 🔄 Migración de Funciones Legacy

```mermaid
flowchart LR
    subgraph "📜 ACTUAL"
        A1[agno_prompts_clean.py]
        A2[agno_prompts.py]
        A3[browser_prompts.py]
        A4[code_gen_prompts.py]
        A5[*_prompts.py files]
    end
    
    subgraph "🔄 PROCESO"
        B1[Extraer Prompts]
        B2[Crear Markdown]
        B3[Validar Estructura]
        B4[Crear Metadata]
    end
    
    subgraph "✅ NUEVO SISTEMA"
        C1[prompts/*/enhance.md]
        C2[prompts/*/generation.md]
        C3[prompts/*/metadata.json]
        C4[PromptService]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    
    B4 --> C1
    B4 --> C2
    B4 --> C3
    B4 --> C4
    
    style A1 fill:#ffebee
    style A2 fill:#ffebee
    style A3 fill:#ffebee
    style A4 fill:#ffebee
    style A5 fill:#ffebee
    style C1 fill:#e8f5e8
    style C2 fill:#e8f5e8
    style C3 fill:#e8f5e8
    style C4 fill:#e8f5e8
```

## 🧩 Componentes Clave a Implementar

### 1. **MarkdownPromptLoader** 🔧
```python
# src/Core/markdown_prompt_loader.py
class MarkdownPromptLoader:
    def __init__(self, prompts_dir: str = "prompts"):
        self.prompts_dir = prompts_dir
        self._cache = {}
    
    def load_prompt(self, category: str, prompt_id: str, language: str) -> str
    def get_available_prompts(self) -> Dict[str, List[str]]
    def validate_prompt(self, category: str, prompt_id: str) -> bool
    def clear_cache(self) -> None
```

### 2. **PromptMarkdownParser** 📝
```python
# src/Core/prompt_markdown_parser.py
class PromptMarkdownParser:
    def parse_prompt_file(self, file_path: str) -> Dict[str, Any]
    def extract_variables(self, prompt_text: str) -> List[str]
    def substitute_variables(self, prompt_text: str, **kwargs) -> str
    def validate_markdown_structure(self, content: str) -> List[str]
```

### 3. **PromptValidator** ✅
```python
# src/Core/prompt_validator.py
class PromptValidator:
    def validate_structure(self, prompt_file: str) -> List[str]
    def validate_variables(self, prompt_text: str, required_vars: List[str]) -> bool
    def validate_translations(self, prompt_data: Dict) -> List[str]
    def validate_metadata(self, metadata_file: str) -> List[str]
```

### 4. **PromptService** (Evolucionado) 🚀
```python
# src/Core/prompt_service.py
class PromptService:
    def __init__(self, prompts_dir: str = "prompts"):
        self.loader = MarkdownPromptLoader(prompts_dir)
        self.parser = PromptMarkdownParser()
        self.validator = PromptValidator()
        self.llm = ChatGoogleGenerativeAI(...)
    
    def execute_prompt(self, category: str, prompt_id: str, 
                      language: str = "en", **variables) -> str
    
    # Métodos de conveniencia
    def enhance_user_story(self, user_story: str, language: str = "en") -> str
    def generate_test_cases(self, user_story: str, language: str = "en") -> str
    def generate_gherkin(self, test_cases: str, language: str = "en") -> str
    def generate_code(self, framework: str, gherkin: str, 
                     history: Dict, language: str = "en") -> str
```

## 📋 Formato de Archivo Markdown Estándar

```markdown
# [Título del Prompt]

## Purpose
[Descripción clara del propósito del prompt]

## Input Format
- Variable 1: Descripción
- Variable 2: Descripción

## Output Format
- Formato esperado de la respuesta
- Estructura de datos si aplica

## English Prompt
[Prompt completo en inglés con variables {variable_name}]

## Spanish Prompt
[Prompt completo en español con variables {variable_name}]

## Variables
- `{variable_name}` - Descripción detallada de la variable

## Examples

### Input
```
[Ejemplo de entrada]
```

### Output
```
[Ejemplo de salida esperada]
```

## Validation Rules
- Regla 1: Descripción
- Regla 2: Descripción

## Version History
- v1.0.0 (2025-05-30): Creación inicial
```

## 🎯 Puntos de Integración con Sistema Actual

```mermaid
flowchart TD
    subgraph "🖥️ UI Layer (Actual)"
        A[app.py - Streamlit]
        B[QA Assistant Page]
        C[Project Manager UI]
    end
    
    subgraph "🔧 Service Layer (Modificar)"
        D[test_service.py]
        E[agents.py]
        F[utils.py]
    end
    
    subgraph "⚡ Prompt System (NUEVO)"
        G[PromptService]
        H[MarkdownPromptLoader]
        I[Prompts/*.md]
    end
    
    A -.->|"Reemplazar llamadas directas\na prompts embebidos"| G
    B -.->|"Usar PromptService\nen lugar de funciones legacy"| G
    C -.->|"Integrar con nuevo sistema"| G
    
    D -->|"Migrar a PromptService"| G
    E -->|"Usar prompts externos"| G
    F -->|"Simplificar lógica"| G
    
    G --> H
    H --> I
    
    style G fill:#4caf50,color:#fff
    style H fill:#2196f3,color:#fff
    style I fill:#ff9800,color:#fff
```

## 🚀 Acciones Inmediatas - Checklist de Implementación

### **DÍA 1: Estructura Base** ✅
- [ ] Crear directorios: `mkdir -p prompts/{user-story,test-cases,code-generation,test-analysis,browser-automation,shared}`
- [ ] Crear `src/Core/markdown_prompt_loader.py`
- [ ] Crear `src/Core/prompt_markdown_parser.py`
- [ ] Test básico de lectura de archivos Markdown

### **DÍA 2-3: Parser y Validator** 🔧
- [ ] Implementar parsing de secciones Markdown
- [ ] Implementar extracción de variables `{variable_name}`
- [ ] Crear `src/Core/prompt_validator.py`
- [ ] Tests unitarios para parser y validator

### **DÍA 4: Primer Prompt Migrado** 📝
- [ ] Migrar `user_story_enhance` a `prompts/user-story/enhance.md`
- [ ] Crear `prompts/user-story/metadata.json`
- [ ] Integrar con `PromptService`
- [ ] Test E2E completo

### **DÍA 5: PromptService Completo** 🎯
- [ ] Implementar cache de prompts
- [ ] Métodos de conveniencia para cada tipo de prompt
- [ ] Integración con LLM existente
- [ ] Logging y error handling

### **SEMANA 2: Migración Masiva** 📦
- [ ] Migrar todos los prompts estructurados
- [ ] Extraer prompts de `agno_prompts_clean.py`
- [ ] Validación automática de todos los prompts
- [ ] CLI básico para gestión

### **SEMANA 3: Integración** 🔗
- [ ] Actualizar `app.py` para usar `PromptService`
- [ ] Reemplazar llamadas legacy en toda la aplicación
- [ ] Tests de integración E2E
- [ ] Performance testing

## 💡 Beneficios Inmediatos Post-Implementación

### **Para el Equipo** 👥
- ✅ **Product Managers** pueden editar prompts sin código
- ✅ **QA Engineers** pueden crear nuevos prompts independientemente  
- ✅ **Developers** se enfocan en lógica, no en prompts

### **Para el Sistema** 🏗️
- ✅ **Versionado granular** de cada prompt individual
- ✅ **Testing independiente** de prompts
- ✅ **Cache automático** para mejor performance
- ✅ **Validación automática** para evitar errores

### **Para Escalabilidad** 📈
- ✅ **Fácil agregar nuevos frameworks** de testing
- ✅ **Soporte multiidioma** nativo
- ✅ **Separación clara** entre prompts y lógica
- ✅ **Arquitectura modular** para crecimiento futuro

## 🎯 Criterios de Éxito Específicos

### **Técnicos** ⚙️
- ✅ 100% prompts migrados a Markdown (0 prompts en código Python)
- ✅ Tiempo de carga < 200ms para prompts cacheados
- ✅ Validación automática con 0 errores en CI/CD
- ✅ Cobertura de tests > 90% en nuevo sistema

### **Funcionales** 🎪
- ✅ Usuario no técnico puede editar prompt y ver cambios en < 5 min
- ✅ Sistema mantiene o mejora performance actual
- ✅ Hot reload de prompts sin reiniciar aplicación
- ✅ Rollback inmediato en caso de problemas

---

**📅 Fecha de Creación:** 30 Mayo 2025  
**👨‍💻 Arquitecto:** GitHub Copilot  
**🏷️ Estado:** Listo para Implementación
