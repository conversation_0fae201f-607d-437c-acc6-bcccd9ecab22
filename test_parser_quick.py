#!/usr/bin/env python3
"""
Quick test to verify the prompt parser fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.Core.prompt_markdown_parser import Prompt<PERSON><PERSON>down<PERSON>arser

def test_parser():
    print("🧪 Testing prompt parser fixes...")
    
    parser = PromptMarkdownParser()
    
    # Test the problematic file that was causing 500 errors
    test_file = "prompts/test-cases/manual-generation.md"
    
    try:
        print(f"📝 Testing file: {test_file}")
        result = parser.parse_prompt_file(test_file)
        
        print("✅ Parser succeeded!")
        print(f"📋 Parsed sections: {list(result.keys())}")
        
        # Check if required sections exist
        if 'English Prompt' in result:
            print("✅ English Prompt found")
        else:
            print("❌ English Prompt missing")
            
        if 'Spanish Prompt' in result:
            print("✅ Spanish Prompt found")
        else:
            print("⚠️  Spanish Prompt missing (optional)")
            
        return True
        
    except Exception as e:
        print(f"❌ Parser failed: {str(e)}")
        return False

def test_validation():
    print("\n🔍 Testing validation...")
    
    parser = PromptMarkdownParser()
    
    try:
        # Test validation with a mock prompt structure
        sections = {
            'English Prompt': 'Test prompt content',
            'Purpose': 'Test purpose'
        }
        
        errors = parser.validate_structure(sections, "test")
        
        if not errors:
            print("✅ Validation passed for minimal structure")
        else:
            print(f"⚠️  Validation warnings: {errors}")
            
        return True
        
    except Exception as e:
        print(f"❌ Validation failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting prompt parser tests...\n")
    
    parser_ok = test_parser()
    validation_ok = test_validation()
    
    print(f"\n📊 Results:")
    print(f"Parser test: {'✅ PASS' if parser_ok else '❌ FAIL'}")
    print(f"Validation test: {'✅ PASS' if validation_ok else '❌ FAIL'}")
    
    if parser_ok and validation_ok:
        print("\n🎉 All tests passed! The parser fixes should resolve the 500 errors.")
    else:
        print("\n💥 Some tests failed. The 500 errors may persist.")
