{"category": "test-analysis", "displayName": "Test Analysis", "description": "Prompts for analyzing and summarizing test execution results and performance metrics", "version": "1.0.0", "maintainer": "AgentQA Team", "tags": ["test-analysis", "reporting", "summarization", "test-results"], "defaultLanguage": "en", "supportedLanguages": ["en", "es"], "commonVariables": {"test_results": {"type": "string", "description": "Test execution results to be analyzed and summarized", "required": true, "example": "Test Suite: Login Tests\n- test_valid_login: PASSED\n- test_invalid_email: FAILED (Error: Element not found)\n- test_empty_fields: PASSED"}}, "prompts": [{"id": "results-summary", "name": "Test Results Summary", "displayName": "Test Results Summary", "description": "Analyzes and summarizes test execution results with actionable insights", "file": "results-summary.md", "languages": ["en", "es"], "variables": ["test_results"], "outputs": ["summary_report"], "examples": [{"description": "Login test results analysis", "input": {"test_results": "Login Tests: 2 passed, 1 failed\nFailure: Invalid email validation not working"}, "expectedOutput": "Structured summary with status, findings, and recommendations"}]}], "validationRules": {"requiredSections": ["## Prompt", "## Variables", "## Expected Output"], "allowedLanguages": ["en", "es"], "variableFormat": "{{variable_name}}", "translationRequired": true}}