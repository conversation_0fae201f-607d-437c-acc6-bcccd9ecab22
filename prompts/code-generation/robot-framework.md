# Robot Framework Code Generation

## Purpose
Generates Robot Framework code with SeleniumLibrary based on Gherkin scenarios and agent execution details.

## Input Format
- Gherkin scenario steps to convert into automated test code
- Base URL of the application under test
- Element selectors identified during agent execution
- Actions performed by the agent during execution

## Output Format
- Complete Robot Framework test automation project
- Reusable keywords for common actions
- Test suites matching Gherkin scenarios

## English Prompt
Generate Robot Framework code based on the following:

Gherkin Steps:
```gherkin
{gherkin_steps}
```

Agent Execution Details:
- Base URL: {base_url}
- Element Selectors: {selectors}
- Actions Performed: {actions}
- Extracted Content: {extracted_content}

The code should follow these requirements:
1. Use Robot Framework with SeleniumLibrary
2. Create reusable keywords for common actions
3. Organize test cases to match Gherkin scenarios
4. Include proper setup and teardown procedures
5. <PERSON><PERSON> waits appropriately for dynamic elements
6. Include error handling and logging
7. Be well-commented and maintainable

## Spanish Prompt
Genera codigo de Robot Framework basado en lo siguiente:

Pasos <PERSON>kin:
```gherkin
{gherkin_steps}
```

Detalles de la ejecución del agente:
- URL base: {base_url}
- Selectores de elementos: {selectors}
- Acciones realizadas: {actions}
- Contenido extraído: {extracted_content}

El codigo debe cumplir estos requisitos:
1. Usar Robot Framework con SeleniumLibrary
2. Crear keywords reutilizables para acciones comunes
3. Organizar casos de prueba para que coincidan con los escenarios Gherkin
4. Incluir procedimientos adecuados de configuración y limpieza
5. Manejar esperas adecuadamente para elementos dinámicos
6. Incluir manejo de errores y registro
7. Estar bien comentado y ser mantenible

## Variables
- `gherkin_steps`: Gherkin scenario steps to convert into automated test code
- `base_url`: The base URL of the application under test
- `selectors`: Element selectors identified during agent execution
- `actions`: Actions performed by the agent during execution
- `extracted_content`: Content extracted during agent execution for validation

## Examples

This section demonstrates how to generate Robot Framework code from Gherkin scenarios.

### Input:
```
gherkin_steps: "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"
base_url: "https://example.com"
selectors: "#email, #password, .login-button"
actions: "Navigate to login page, fill email field, fill password field, click login button"
extracted_content: "Welcome message on dashboard"
```

### Output:
Complete Robot Framework test automation project with reusable keywords, test suites, and proper configuration.
