# Robot Framework Code Generation

## Description
Generates Robot Framework code with SeleniumLibrary based on Gherkin scenarios and agent execution details.

## Prompt

### English
Generate Robot Framework code based on the following:

Gherkin Steps:
```gherkin
{{gherkin_steps}}
```

Agent Execution Details:
- Base URL: {{base_url}}
- Element Selectors: {{selectors}}
- Actions Performed: {{actions}}
- Extracted Content: {{extracted_content}}

The code should follow these requirements:
1. Use Robot Framework with SeleniumLibrary
2. Create reusable keywords for common actions
3. Organize test cases to match Gherkin scenarios
4. Include proper setup and teardown procedures
5. <PERSON><PERSON> waits appropriately for dynamic elements
6. Include error handling and logging
7. Be well-commented and maintainable

### Spanish
Genera codigo de Robot Framework basado en lo siguiente:

Pasos <PERSON>:
```gherkin
{{gherkin_steps}}
```

Detalles de la ejecución del agente:
- URL base: {{base_url}}
- Selectores de elementos: {{selectors}}
- Acciones realizadas: {{actions}}
- Contenido extraído: {{extracted_content}}

El codigo debe cumplir estos requisitos:
1. Usar Robot Framework con SeleniumLibrary
2. Crear keywords reutilizables para acciones comunes
3. Organizar casos de prueba para que coincidan con los escenarios Gherkin
4. Incluir procedimientos adecuados de configuración y limpieza
5. Manejar esperas adecuadamente para elementos dinámicos
6. Incluir manejo de errores y registro
7. Estar bien comentado y ser mantenible

## Variables

- **gherkin_steps** (required): Gherkin scenario steps to convert into automated test code
- **base_url** (required): The base URL of the application under test
- **selectors** (required): Element selectors identified during agent execution
- **actions** (required): Actions performed by the agent during execution
- **extracted_content** (optional): Content extracted during agent execution for validation

## Expected Output

Complete Robot Framework test automation project including:

1. **Test suite files** (.robot) with test cases matching Gherkin scenarios
2. **Resource files** with reusable keywords
3. **Page Object keywords** for element interactions
4. **Setup and teardown** procedures
5. **Variables file** with configuration settings
6. **Requirements file** with necessary dependencies
7. **Well-documented keywords** with proper logging

## Example

### Input:
```
gherkin_steps: "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"
base_url: "https://example.com"
selectors: "#email, #password, .login-button"
actions: "Navigate to login page, fill email field, fill password field, click login button"
extracted_content: "Welcome message on dashboard"
```

### Output:
Complete Robot Framework test automation project with reusable keywords, test suites, and proper configuration.

## Tags
- robot-framework
- selenium-library
- keywords
- test-automation
- bdd
- data-driven

## Framework Requirements
- robotframework
- robotframework-seleniumlibrary
- webdrivermanager

## Version
1.0.0

## Last Updated
2024-12-19
