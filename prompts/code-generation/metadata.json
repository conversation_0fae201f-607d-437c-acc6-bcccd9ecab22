{"category": "code-generation", "displayName": "Code Generation", "description": "Prompts for generating automated test code in various frameworks from Gherkin scenarios and agent execution details", "version": "1.0.0", "maintainer": "AgentQA Team", "tags": ["code-generation", "automation", "frameworks", "g<PERSON>kin", "test-automation"], "defaultLanguage": "en", "supportedLanguages": ["en", "es"], "commonVariables": {"gherkin_steps": {"type": "string", "description": "<PERSON>herkin scenario steps to convert into automated test code", "required": true, "example": "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"}, "base_url": {"type": "string", "description": "The base URL of the application under test", "required": true, "example": "https://example.com"}, "selectors": {"type": "string", "description": "Element selectors identified during agent execution", "required": true, "example": "#email, #password, .login-button"}, "actions": {"type": "string", "description": "Actions performed by the agent during execution", "required": true, "example": "Navigate to login page, fill email field, fill password field, click login button"}, "extracted_content": {"type": "string", "description": "Content extracted during agent execution for validation", "required": false, "example": "Welcome message, user dashboard, error messages"}}, "prompts": [{"id": "selenium-pytest", "name": "Selenium PyTest BDD", "displayName": "Selenium PyTest BDD", "description": "Generates Selenium PyTest BDD code with Page Object Model", "file": "selenium-pytest.md", "languages": ["en", "es"], "variables": ["gherkin_steps", "base_url", "selectors", "actions", "extracted_content"], "outputs": ["python_code"], "frameworks": ["selenium", "pytest", "pytest-bdd"]}, {"id": "playwright", "name": "Playwright <PERSON>", "displayName": "Playwright <PERSON>", "description": "Generates Playwright Python code with async/await syntax", "file": "playwright.md", "languages": ["en", "es"], "variables": ["gherkin_steps", "base_url", "selectors", "actions", "extracted_content"], "outputs": ["python_code"], "frameworks": ["playwright", "python"]}, {"id": "cypress", "name": "Cypress JavaScript", "displayName": "Cypress JavaScript", "description": "Generates Cypress JavaScript code with Cucumber integration", "file": "cypress.md", "languages": ["en", "es"], "variables": ["gherkin_steps", "base_url", "selectors", "actions", "extracted_content"], "outputs": ["javascript_code"], "frameworks": ["cypress", "cucumber", "javascript"]}, {"id": "robot-framework", "name": "Robot Framework", "displayName": "Robot Framework", "description": "Generates Robot Framework code with SeleniumLibrary", "file": "robot-framework.md", "languages": ["en", "es"], "variables": ["gherkin_steps", "base_url", "selectors", "actions", "extracted_content"], "outputs": ["robot_code"], "frameworks": ["robot-framework", "selenium"]}, {"id": "java-selenium", "name": "Java Selenium Cucumber", "displayName": "Java Selenium Cucumber", "description": "Generates Java Selenium code with Cucumber and Page Object Model", "file": "java-selenium.md", "languages": ["en", "es"], "variables": ["gherkin_steps", "base_url", "selectors", "actions", "extracted_content"], "outputs": ["java_code"], "frameworks": ["selenium", "cucumber", "java", "maven"]}], "validationRules": {"requiredSections": ["## Prompt", "## Variables", "## Expected Output"], "allowedLanguages": ["en", "es"], "variableFormat": "{{variable_name}}", "translationRequired": true}}