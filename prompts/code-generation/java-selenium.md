# Java Selenium Cucumber Code Generation

## Purpose
Generates Java Selenium code with Cucumber and Page Object Model design pattern based on Gherkin scenarios and agent execution details.

## Input Format
- Gherkin scenario steps to convert into automated test code
- Base URL of the application under test
- Element selectors identified during agent execution
- Actions performed by the agent during execution

## Output Format
- Complete Java test automation project with Cucumber integration
- Page Object classes following POM design pattern
- Maven/Gradle configuration with dependencies

## English Prompt
Generate Java Selenium Cucumber code based on the following:

Gherkin Steps:
```gherkin
{gherkin_steps}
```

Agent Execution Details:
- Base URL: {base_url}
- Element Selectors: {selectors}
- Actions Performed: {actions}
- Extracted Content: {extracted_content}

The code should follow these requirements:
1. Use Java with Selenium WebDriver and Cucumber
2. Implement a Page Object Model design pattern
3. Use Maven or Gradle for project structure
4. Include robust element location strategies
5. <PERSON><PERSON> waits appropriately for dynamic elements
6. Include proper error handling and reporting
7. Be well-commented and maintainable
8. Support multiple browsers

## Spanish Prompt
Genera codigo Java de Selenium con Cucumber basado en lo siguiente:

Pasos <PERSON>:
```gherkin
{gherkin_steps}
```

Detalles de la ejecución del agente:
- URL base: {base_url}
- Selectores de elementos: {selectors}
- Acciones realizadas: {actions}
- Contenido extraído: {extracted_content}

El codigo debe cumplir estos requisitos:
1. Usar Java con Selenium WebDriver y Cucumber
2. Implementar un patrón de diseño Page Object Model
3. Usar Maven o Gradle para la estructura del proyecto
4. Incluir estrategias robustas de localización de elementos
5. Manejar esperas adecuadamente para elementos dinámicos
6. Incluir manejo de errores e informes apropiados
7. Estar bien comentado y ser mantenible
8. Soportar múltiples navegadores

## Variables
- `gherkin_steps`: Gherkin scenario steps to convert into automated test code
- `base_url`: The base URL of the application under test
- `selectors`: Element selectors identified during agent execution
- `actions`: Actions performed by the agent during execution
- `extracted_content`: Content extracted during agent execution for validation

## Examples

This section demonstrates how to generate Java Selenium Cucumber code from Gherkin scenarios.

### Input:
```
gherkin_steps: "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"
base_url: "https://example.com"
selectors: "#email, #password, .login-button"
actions: "Navigate to login page, fill email field, fill password field, click login button"
extracted_content: "Welcome message on dashboard"
```

### Output:
Complete Java test automation project with Cucumber integration, page objects, and Maven/Gradle configuration.
