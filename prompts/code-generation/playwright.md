# Playwright Python Code Generation

## Description
Generates Playwright Python code with async/await syntax and Page Object Model design pattern based on Gherkin scenarios and agent execution details.

## Prompt

### English
Generate Playwright Python code based on the following:

Gherkin Steps:
```gherkin
{{gherkin_steps}}
```

Agent Execution Details:
- Base URL: {{base_url}}
- Element Selectors: {{selectors}}
- Actions Performed: {{actions}}
- Extracted Content: {{extracted_content}}

The code should follow these requirements:
1. Use Playwright's Python API with async/await syntax
2. Implement a Page Object Model design pattern
3. Utilize <PERSON><PERSON>'s auto-waiting capabilities
4. Include proper error handling and reporting
5. Support multiple browsers (Chromium, Firefox, WebKit)
6. Be well-commented and maintainable
7. Include setup and teardown routines

### Spanish
Genera CODIGO de Playwright con Python basado en lo siguiente:

Pasos <PERSON>kin:
```gherkin
{{gherkin_steps}}
```

Detalles de la ejecución del agente:
- URL base: {{base_url}}
- Selectores de elementos: {{selectors}}
- Acciones realizadas: {{actions}}
- Contenido extraído: {{extracted_content}}

El codigo debe cumplir estos requisitos:
1. Usar la API de Python de Playwright con sintaxis async/await
2. Implementar un patrón de diseño Page Object Model
3. Utilizar las capacidades de auto-espera de Playwright
4. Incluir manejo de errores y reportes apropiados
5. Soportar múltiples navegadores (Chromium, Firefox, WebKit)
6. Estar bien comentado y ser mantenible
7. Incluir rutinas de configuración y limpieza

## Variables

- **gherkin_steps** (required): Gherkin scenario steps to convert into automated test code
- **base_url** (required): The base URL of the application under test
- **selectors** (required): Element selectors identified during agent execution
- **actions** (required): Actions performed by the agent during execution
- **extracted_content** (optional): Content extracted during agent execution for validation

## Expected Output

Complete Python test automation code including:

1. **Test file** with async/await test functions
2. **Page Object classes** with async methods following POM pattern
3. **Configuration file** with browser setup and teardown
4. **Requirements file** with Playwright dependencies
5. **Multi-browser support** configuration
6. **Auto-waiting strategies** leveraging Playwright capabilities
7. **Well-commented async code** for maintainability

## Example

### Input:
```
gherkin_steps: "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"
base_url: "https://example.com"
selectors: "#email, #password, .login-button"
actions: "Navigate to login page, fill email field, fill password field, click login button"
extracted_content: "Welcome message on dashboard"
```

### Output:
Complete Python test automation project with Playwright async implementation, page objects, and multi-browser configuration.

## Tags
- playwright
- python
- async-await
- page-object-model
- multi-browser
- test-automation
- auto-waiting

## Framework Requirements
- playwright
- pytest-asyncio
- pytest

## Version
1.0.0

## Last Updated
2024-12-19
