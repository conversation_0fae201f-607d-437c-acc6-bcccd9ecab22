# Selenium PyTest BDD Code Generation

## Purpose
Generates Selenium PyTest BDD code with Page Object Model design pattern based on Gherkin scenarios and agent execution details.

## Input Format
- Gherkin scenario steps to convert into automated test code
- Base URL of the application under test
- Element selectors identified during agent execution
- Actions performed by the agent during execution

## Output Format
- Complete Python test automation code with pytest-bdd implementation
- Page Object classes following POM design pattern
- Configuration files and dependencies

## English Prompt
Generate Selenium PyTest BDD code based on the following:

Gherkin Steps:
```gherkin
{gherkin_steps}
```

Agent Execution Details:
- Base URL: {base_url}
- Element Selectors: {selectors}
- Actions Performed: {actions}
- Extracted Content: {extracted_content}

The code should follow these requirements:
1. Use pytest-bdd framework to implement the Gherkin steps
2. Implement a Page Object Model design pattern
3. Include robust element location strategies
4. <PERSON><PERSON> waits appropriately for dynamic elements
5. Include proper error handling and reporting
6. Be well-commented and maintainable
7. Include a conftest.py with proper fixture setup

## Spanish Prompt
Genera codigo de Selenium PyTest BDD basado en lo siguiente:

Pasos <PERSON>:
```gherkin
{gherkin_steps}
```

Detalles de la ejecución del agente:
- URL base: {base_url}
- Selectores de elementos: {selectors}
- Acciones realizadas: {actions}
- Contenido extraído: {extracted_content}

El codigo debe cumplir estos requisitos:
1. Usar el framework pytest-bdd para implementar los pasos Gherkin
2. Implementar un patrón de diseño Page Object Model
3. Incluir estrategias robustas de localización de elementos
4. Manejar esperas adecuadamente para elementos dinámicos
5. Incluir manejo de errores y reportes apropiados
6. Estar bien comentado y ser mantenible
7. Incluir un conftest.py con la configuración adecuada de fixtures

## Variables
- `gherkin_steps`: Gherkin scenario steps to convert into automated test code
- `base_url`: The base URL of the application under test
- `selectors`: Element selectors identified during agent execution
- `actions`: Actions performed by the agent during execution
- `extracted_content`: Content extracted during agent execution for validation

## Examples

This section demonstrates how to generate Selenium PyTest BDD code from Gherkin scenarios.

### Input:
```
gherkin_steps: "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"
base_url: "https://example.com"
selectors: "#email, #password, .login-button"
actions: "Navigate to login page, fill email field, fill password field, click login button"
extracted_content: "Welcome message on dashboard"
```

### Output:
Complete Python test automation project with pytest-bdd implementation, page objects, and configuration files.
