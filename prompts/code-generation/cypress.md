# Cypress JavaScript Code Generation

## Purpose
Generates Cypress JavaScript code with Cucumber integration and Page Object Model pattern based on Gherkin scenarios and agent execution details.

## Input Format
- Gherkin scenario steps to convert into automated test code
- Base URL of the application under test
- Element selectors identified during agent execution
- Actions performed by the agent during execution

## Output Format
- Complete Cypress test automation project with Cucumber integration
- Page Object classes following POM design pattern
- Configuration files and custom commands

## English Prompt
Generate Cypress JavaScript code based on the following:

Gherkin Steps:
```gherkin
{gherkin_steps}
```

Agent Execution Details:
- Base URL: {base_url}
- Element Selectors: {selectors}
- Actions Performed: {actions}
- Extracted Content: {extracted_content}

The code should follow these requirements:
1. Use Cypress with Cucumber/Gherkin integration
2. Include a Page Object Model pattern
3. Leverage Cypress's built-in waiting and retrying capabilities
4. Include custom commands where appropriate
5. Configure proper reporting and screenshots
6. Be well-commented and maintainable
7. Include cypress.json configuration

## Spanish Prompt
Genera codigo JavaScript de Cypress basado en lo siguiente:

Paso<PERSON>:
```gherkin
{gherkin_steps}
```

Detalles de la ejecución del agente:
- URL base: {base_url}
- Selectores de elementos: {selectors}
- Acciones realizadas: {actions}
- Contenido extraído: {extracted_content}

El codigo debe cumplir estos requisitos:
1. Usar Cypress con integración de Cucumber/Gherkin
2. Incluir un patrón de Page Object Model
3. Aprovechar las capacidades incorporadas de espera y reintento de Cypress
4. Incluir comandos personalizados donde sea apropiado
5. Configurar informes y capturas de pantalla adecuados
6. Estar bien comentado y ser mantenible
7. Incluir configuración de cypress.json

## Variables
- `gherkin_steps`: Gherkin scenario steps to convert into automated test code
- `base_url`: The base URL of the application under test
- `selectors`: Element selectors identified during agent execution
- `actions`: Actions performed by the agent during execution
- `extracted_content`: Content extracted during agent execution for validation

## Examples

This section demonstrates how to generate Cypress JavaScript code from Gherkin scenarios.

### Input:
```
gherkin_steps: "Given I am on the login page\nWhen I enter valid credentials\nThen I should be logged in"
base_url: "https://example.com"
selectors: "#email, #password, .login-button"
actions: "Navigate to login page, fill email field, fill password field, click login button"
extracted_content: "Welcome message on dashboard"
```

### Output:
Complete Cypress test automation project with Cucumber integration, page objects, and proper configuration.
