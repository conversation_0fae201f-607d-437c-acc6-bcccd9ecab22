{"category": "test-cases", "displayName": "Test Case Generation", "description": "Prompts for generating manual and automated test cases from user stories and requirements", "version": "1.0.0", "maintainer": "AgentQA Team", "tags": ["testing", "test-cases", "manual-testing", "automation"], "defaultLanguage": "en", "supportedLanguages": ["en", "es"], "commonVariables": {"user_story": {"type": "string", "description": "The user story or requirement to generate test cases from", "required": true, "example": "As a user, I want to be able to login to access my account"}, "additional_context": {"type": "string", "description": "Additional context or requirements for test case generation", "required": false, "example": "Focus on edge cases and error handling"}, "test_type": {"type": "string", "description": "Type of test cases to generate (manual, automated, both)", "required": false, "default": "manual", "enum": ["manual", "automated", "both"]}, "manual_test_cases_markdown": {"type": "string", "description": "Manual test cases in markdown format to be converted to Gherkin scenarios", "required": true, "example": "Test Case 1: Login validation\nPreconditions: User account exists\nSteps: 1. Open login page 2. Enter credentials\nExpected: Successful login"}}, "prompts": [{"id": "manual-generation", "name": "Manual Test Case Generation", "displayName": "Manual Test Case Generation", "description": "Generates comprehensive manual test cases from user stories", "file": "manual-generation.md", "languages": ["en", "es"], "variables": ["user_story", "additional_context", "test_type"], "outputs": ["test_cases"], "examples": [{"description": "Login functionality test cases", "input": {"user_story": "As a user, I want to be able to login with email and password", "additional_context": "Include security and validation tests"}, "expectedOutput": "Structured test cases with steps, expected results, and edge cases"}]}, {"id": "gherkin-conversion", "name": "<PERSON><PERSON><PERSON> Conversion", "displayName": "<PERSON><PERSON><PERSON> Conversion", "description": "Converts manual test cases into well-structured Gherkin scenarios for BDD", "file": "gherkin-conversion.md", "languages": ["en", "es"], "variables": ["manual_test_cases_markdown"], "outputs": ["gherkin_scenarios"], "examples": [{"description": "Convert login test cases to <PERSON><PERSON><PERSON>", "input": {"manual_test_cases_markdown": "Test Case 1: Login with valid credentials\nPreconditions: User has a valid account\nSteps: 1. Navigate to login page 2. Enter email and password 3. Click login button\nExpected Result: User is redirected to dashboard"}, "expectedOutput": "Clean Gherkin scenarios with Feature and Scenario definitions"}]}], "validationRules": {"requiredSections": ["## Prompt", "## Variables", "## Expected Output"], "allowedLanguages": ["en", "es"], "variableFormat": "{{variable_name}}", "translationRequired": true}}