# Gherkin Conversion Prompt

## Purpose
Converts manual test cases into well-structured Gherkin scenarios using BDD (Behavior-Driven Development) format.

## Input Format
- Manual test cases in markdown format
- Each test case should have: title, preconditions, actions, and expected results

## Output Format
- Well-structured Gherkin scenarios
- Feature description at the top
- Individual scenarios for each test case
- Clear Given-When-Then structure

## English Prompt
Your task is to convert the following manual test cases into well-structured Gherkin scenarios.

Manual test cases:
{manual_test_cases_markdown}

Convert these test cases into Gherkin scenarios following these guidelines:

1. Use the keywords: Feature, Scenario, Given, When, Then, And, But
2. Create a Feature that represents the general functionality
3. Create individual scenarios for each test case
4. Keep the steps clear, concise, and in present imperative format
5. Maintain the same preconditions, actions, and expected results
6. Use business-oriented language, not technical implementation details
7. If there are example tables or scenario outlines, use them appropriately
8. Preserve any URLs that appear in the original test cases
9. Adequately represent negative test cases

IMPORTANT: Return ONLY the clean Gherkin scenarios. Do not include any introductory text, explanations, or markdown code blocks (```gherkin). Return only the pure Gherkin content.

## Variables
- `manual_test_cases_markdown`: The input manual test cases in markdown format

## Examples

### Example Input:
```
## Test Case 1: User Login
**Preconditions:** User has valid credentials
**Actions:**
1. Navigate to login page
2. Enter username and password
3. Click login button
**Expected Results:** User should be redirected to dashboard
```

### Example Output:
```gherkin
Feature: User Authentication
  
  Scenario: Successful user login with valid credentials
    Given the user has valid credentials
    And the user is on the login page
    When the user enters their username and password
    And the user clicks the login button
    Then the user should be redirected to the dashboard
```

### Spanish
Tu tarea es convertir los siguientes casos de prueba manuales en escenarios Gherkin bien estructurados.

Casos de prueba manuales:
{{manual_test_cases_markdown}}

Convierte estos casos de prueba en escenarios Gherkin siguiendo estas directrices:

1. Usa las keywords: Feature, Scenario, Given, When, Then, And, But
2. Crea un Feature que represente la funcionalidad general
3. Crea escenarios individuales para cada caso de prueba
4. Mantiene los pasos claros, concisos y en formato imperativo presente
5. Conserva las mismas precondiciones, acciones y resultados esperados
6. Usa lenguaje orientado al negocio, no detalles técnicos de implementación
7. Si hay tablas de ejemplos o escenarios outline, úsalos adecuadamente
8. Conserva cualquier URL que aparezca en los casos de prueba originales
9. Representa adecuadamente los casos de prueba negativos

IMPORTANTE: Devuelve ÚNICAMENTE los escenarios Gherkin limpios. No incluyas texto introductorio, explicaciones o bloques de código markdown (```gherkin). Devuelve solo el contenido Gherkin puro.

## Variables

- **manual_test_cases_markdown** (required): Manual test cases in markdown format to be converted to Gherkin scenarios

## Expected Output

Clean Gherkin scenarios without any introductory text, explanations, or markdown code blocks. The output should contain:

1. Feature definition representing the general functionality
2. Individual scenarios for each test case
3. Proper Gherkin structure with Given/When/Then steps
4. Business-oriented language
5. Preserved URLs and negative test cases where applicable

## Tags
- gherkin
- bdd
- test-conversion
- automation
- behavior-driven-development

## Version
1.0.0

## Last Updated
2024-12-19
