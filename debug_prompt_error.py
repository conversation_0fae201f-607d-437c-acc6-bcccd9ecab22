#!/usr/bin/env python3
"""
Debug script to reproduce the 500 error with prompt details endpoint
"""

import sys
import os
import json
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.abspath('.'))

def test_file_access():
    """Test basic file access"""
    print("🔍 Testing file access...")
    
    prompts_dir = Path("prompts")
    category_dir = prompts_dir / "test-cases"
    metadata_file = category_dir / "metadata.json"
    
    print(f"Prompts dir exists: {prompts_dir.exists()}")
    print(f"Category dir exists: {category_dir.exists()}")
    print(f"Metadata file exists: {metadata_file.exists()}")
    
    if metadata_file.exists():
        try:
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            print(f"✅ Metadata loaded successfully")
            print(f"Found {len(metadata.get('prompts', []))} prompts")
            
            # Find manual-generation prompt
            for prompt in metadata.get("prompts", []):
                if prompt.get("id") == "manual-generation":
                    print(f"✅ Found manual-generation prompt: {prompt}")
                    file_path = category_dir / prompt["file"]
                    print(f"Prompt file exists: {file_path.exists()}")
                    return file_path
        except Exception as e:
            print(f"❌ Error loading metadata: {e}")
    
    return None

def test_parser():
    """Test the markdown parser"""
    print("\n🧪 Testing markdown parser...")
    
    try:
        from src.Core.prompt_markdown_parser import PromptMarkdownParser
        print("✅ Parser imported successfully")
        
        file_path = test_file_access()
        if not file_path:
            print("❌ Cannot test parser - file not found")
            return False
            
        parser = PromptMarkdownParser()
        
        # Test file reading
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✅ File read successfully, {len(content)} characters")
        
        # Test parsing
        sections = parser.parse_markdown_content(content, str(file_path))
        print(f"✅ Parsing successful, found {len(sections)} sections")
        print(f"Sections: {list(sections.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Parser test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_route():
    """Test the API route logic"""
    print("\n🌐 Testing API route logic...")
    
    try:
        from src.API.prompt_routes import get_prompt_details
        from src.Core.prompt_markdown_parser import PromptMarkdownParser
        
        # Simulate the API call
        category = "test-cases"
        prompt_id = "manual-generation"
        
        prompts_dir = Path("prompts")
        category_dir = prompts_dir / category
        
        # Load metadata
        metadata_file = category_dir / "metadata.json"
        with open(metadata_file, 'r', encoding='utf-8') as f:
            category_metadata = json.load(f)
        
        # Find prompt in metadata
        prompt_metadata = None
        for prompt in category_metadata.get("prompts", []):
            if prompt.get("id") == prompt_id:
                prompt_metadata = prompt
                break
        
        if not prompt_metadata:
            print(f"❌ Prompt '{prompt_id}' not found in metadata")
            return False
            
        print(f"✅ Found prompt metadata: {prompt_metadata}")
        
        # Load and parse the markdown file
        file_path = category_dir / prompt_metadata["file"]
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        parser = PromptMarkdownParser()
        sections = parser.parse_markdown_content(content, str(file_path))
        
        print(f"✅ Sections parsed: {list(sections.keys())}")
        
        # Test creating the response object
        from src.API.prompt_routes import PromptDetailResponse
        
        response = PromptDetailResponse(
            category=category,
            prompt_id=prompt_id,
            metadata=prompt_metadata,
            content=sections,
            file_path=str(file_path.relative_to(Path.cwd()))
        )
        
        print(f"✅ Response object created successfully")
        print(f"Response category: {response.category}")
        print(f"Response prompt_id: {response.prompt_id}")
        print(f"Response file_path: {response.file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ API route test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting debug tests for prompt details 500 error...\n")
    
    file_ok = test_file_access() is not None
    parser_ok = test_parser()
    api_ok = test_api_route()
    
    print(f"\n📊 Results:")
    print(f"File access: {'✅ PASS' if file_ok else '❌ FAIL'}")
    print(f"Parser test: {'✅ PASS' if parser_ok else '❌ FAIL'}")
    print(f"API route test: {'✅ PASS' if api_ok else '❌ FAIL'}")
    
    if file_ok and parser_ok and api_ok:
        print("\n🎉 All tests passed! The issue might be elsewhere.")
    else:
        print("\n💥 Some tests failed. This explains the 500 error.")
